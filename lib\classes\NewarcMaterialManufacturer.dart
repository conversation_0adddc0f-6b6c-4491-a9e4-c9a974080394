class NewarcMaterialManufacturer{
  String? firebaseId;
  String? name; // Manufacturer Name
  int? insertTimestamp;
  String? uid; //the Firebase Id of the logged in user
  bool? isArchive; //default to false
  int? codeCounter;

  Map<String, Object?> toMap() {
    return {
      'name': name,
      'insertTimestamp': insertTimestamp,
      'uid': uid,
      'isArchive': isArchive,
      'codeCounter': codeCounter,
    };
  }

  NewarcMaterialManufacturer.empty() {
    this.firebaseId = '';
    this.name = '';
    this.insertTimestamp = null;
    this.uid = '';
    this.isArchive = true;
    this.codeCounter = 0;
  }

  NewarcMaterialManufacturer.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.name = data['name'];
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
      this.isArchive = data['isArchive'];
      this.codeCounter = data['codeCounter'];
    } catch (e, s) {
      print({ 'NewarcManufacturer Class Error ------->', e, s});
    }
  }
}