import 'package:cloud_firestore/cloud_firestore.dart';

Future<DocumentSnapshot<Map<String, dynamic>?>> fetchDocument(
    String documentPath) async {
  DocumentSnapshot<Map<String, dynamic>> documentSnapshot =
      await FirebaseFirestore.instance.doc(documentPath).get();

  return documentSnapshot;
}

Future<String> writeDocument(String documentPath, Map<String, dynamic> data,
    {String? id}) async {
  if (id == null) {
    DocumentReference<Map<String, dynamic>> ref =
        await FirebaseFirestore.instance.collection(documentPath).add(data);
    return ref.id;
  } else {
    await FirebaseFirestore.instance.collection(documentPath).doc(id).set(data);
    return id;
  }
}

Future<bool> deleteDocument(
  String collectionPath,
  String docId,
) async {
  try {
    await FirebaseFirestore.instance
        .collection(collectionPath)
        .doc(docId)
        .delete();
  } catch (e, s) {
    // print({e, s});
    return false;
  }

  return true;
}

Future<bool> deleteAllCollectionDocuments(
  String collectionPath,
) async {
  //safety check
  if (!collectionPath.contains("notifications")) {
    return false;
  }
  try {
    QuerySnapshot querySnapshot =
        await FirebaseFirestore.instance.collection(collectionPath).get();

    for (String docId in querySnapshot.docs.map((d) => d.id)) {
      await FirebaseFirestore.instance
          .collection(collectionPath)
          .doc(docId)
          .delete();
    }
  } catch (e,s) {
    // print({e,s});
    return false;
  }

  return true;
}

Future<void> updateDocument(
    String collectionPath, String docId, Map<String, dynamic> data) async {
  try {
    await FirebaseFirestore.instance
        .collection(collectionPath)
        .doc(docId)
        .update(data);
  } catch (e,s) {
    // print({e,s});
  }
}
