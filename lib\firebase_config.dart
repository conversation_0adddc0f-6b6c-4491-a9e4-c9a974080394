import 'package:firebase_core/firebase_core.dart';
import 'package:newarc_materiotech/app_config.dart';
import 'package:newarc_materiotech/firebase_options_staging.dart';
import 'package:newarc_materiotech/firebase_options_prod.dart';

/// Returns the appropriate Firebase options based on the environment
/// determined by the isProduction flag in app_config.dart
class FirebaseConfig {
  static FirebaseOptions get options {
    if (isProduction) {
      return ProductionFirebaseOptions.currentPlatform;
    } else {
      return StagingFirebaseOptions.currentPlatform;
    }
  }
}
