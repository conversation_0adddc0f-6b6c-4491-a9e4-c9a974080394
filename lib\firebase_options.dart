// This file is maintained for backward compatibility
// The actual configuration is now in firebase_options_staging.dart and firebase_options_prod.dart
// and is selected based on the isProduction flag in app_config.dart

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:newarc_materiotech/firebase_config.dart';

/// Default [FirebaseOptions] for use with your Firebase apps.
/// This now delegates to the appropriate environment based on the isProduction flag.
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    return FirebaseConfig.options;
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBq5h5fMtUIE5i0LKSY8B5y8s6SdG_g1dQ',
    appId: '1:10485153929:web:045ee5077dbb8a66ff0658',
    messagingSenderId: '10485153929',
    projectId: 'newarc-staging',
    authDomain: 'newarc-staging.firebaseapp.com',
    databaseURL:
        'https://newarc-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'newarc-staging.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBUJr5WB7942PoxgkFDk1h2mmOtQO7aodQ',
    appId: '1:10485153929:ios:19f1fec31e1dc2ebff0658',
    messagingSenderId: '10485153929',
    projectId: 'newarc-staging',
    databaseURL:
        'https://newarc-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'newarc-staging.appspot.com',
    iosBundleId: 'com.newarc.materioteca',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDWz0RDAOKZpWYlRXxe53YsGqNfscC2hUA',
    appId: '1:10485153929:android:af9333f9e730cbe9ff0658',
    messagingSenderId: '10485153929',
    projectId: 'newarc-staging',
    databaseURL:
        'https://newarc-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'newarc-staging.appspot.com',
    iosBundleId: 'com.newarc.materioteca',
  );
}
