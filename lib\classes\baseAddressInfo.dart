class BaseAddressInfo {
  String? streetName;
  String? postalCode;
  String? province;
  String? region;
  String? country;
  String? latitude;
  String? longitude;
  String? fullAddress;
  String? streetNumber;
  String? city;
  String? locality;


  BaseAddressInfo(
      {this.streetName,
      this.postalCode,
      this.province,
      this.region,
      this.country,
      this.latitude,
      this.longitude,
      this.fullAddress,
      this.streetNumber,
      this.city,
      this.locality});

  BaseAddressInfo.empty() {
    this.streetName = null;
    this.postalCode = null;
    this.province = null;
    this.region = null;
    this.country = null;
    this.latitude = null;
    this.longitude = null;
    this.fullAddress = null;
    this.streetNumber = null;
    this.city = null;
    this.locality = null;
  }

  BaseAddressInfo.fromMap(Map<String,dynamic> data) {

    this.streetName = data['streetName'];
    this.postalCode = data['postalCode'].toString();
    this.province = data['province'];
    this.region = data['region'];
    this.country = data['country'];
    this.latitude = data['latitude'].toString();
    this.longitude = data['longitude'].toString();
    this.fullAddress = data['fullAddress'];
    this.streetNumber = data['streetNumber'];
    this.city = data['city'];
    this.locality = data['locality'];
    if (this.fullAddress == null || this.fullAddress?.isEmpty == true)
      this.fullAddress = this.toFullAddress(); 
  }

  bool isValidAddress () {
    return 
      (this.streetName != null && this.streetName!.isNotEmpty )&&
      (this.streetNumber != null && this.streetNumber!.isNotEmpty )&&
      (this.city != null && this.city!.isNotEmpty )&&
      (this.region != null && this.region!.isNotEmpty );
  }

  Map<String, dynamic> toMap() {
    return {
      'streetName': this.streetName,
      'postalCode': this.postalCode,
      'province': this.province,
      'region': this.region,
      'country': this.country,
      'latitude': this.latitude,
      'longitude': this.longitude,
      'fullAddress': this.fullAddress == null ? this.toFullAddress(): this.fullAddress,
      'streetNumber': this.streetNumber,
      'city': this.city,
      'locality': this.locality
    };
  }

  toFullAddress() {
    var s = '${this.streetName}, ${this.streetNumber}, ${this.locality ?? this.city}, ${this.province}, ${this.country}';
    return s;
  }

  toShortAddress() {
    var s = '${this.streetName} ${this.streetNumber}, ${this.city}';
    return s;
  }
}


void Copy(BaseAddressInfo into, BaseAddressInfo from) {
  into.streetName = from.streetName;
  into.postalCode = from.postalCode;
  into.province = from.province;
  into.region = from.region;
  into.country = from.country;
  into.latitude = from.latitude;
  into.longitude = from.longitude;
  into.fullAddress = from.fullAddress;
  into.city = from.city;
  into.locality = from.locality;
  if (into.fullAddress != null) {
    into.fullAddress = from.fullAddress;
  } else {
    into.fullAddress = from.toFullAddress();
  }
  into.streetNumber = from.streetNumber;
}