import 'package:cloud_firestore/cloud_firestore.dart';
import 'NAMaterial.dart';

class NewarcMateriotechSelection {
  String? id;
  String? name;
  int? insertTimestamp;
  int? modificationTimestamp;
  String? clientId;
  List<NewarcMateriotechRoom> rooms = [];

  NewarcMateriotechSelection(Map<String, dynamic> newarcMateriotechSelectionMap) {
    this.id = newarcMateriotechSelectionMap['id'];
    this.name = newarcMateriotechSelectionMap['name'];
    this.insertTimestamp = newarcMateriotechSelectionMap['insertTimestamp'];
    this.modificationTimestamp = newarcMateriotechSelectionMap['modificationTimestamp'];
    this.clientId = newarcMateriotechSelectionMap['clientId'] ?? "";
    this.rooms = newarcMateriotechSelectionMap['rooms'] ?? [];
  }
  NewarcMateriotechSelection.empty() {
    this.id = null;
    this.name = null;
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.clientId = null;
    this.rooms = [];
  }

  NewarcMateriotechSelection.fromDocument(Map<String, dynamic> data, String id) {
    this.id = id;
    this.name = data['name'];
    this.insertTimestamp = data['insertTimestamp'];
    this.modificationTimestamp = data['modificationTimestamp'];
    this.clientId = data['clientId'];
    if (data['rooms'] != null) {
      for (var i = 0; i < data['rooms'].length; i++) {
        this.rooms.add(NewarcMateriotechRoom.fromMap(data['rooms'][i]));
      }
    } else {
      this.rooms = [];
    }
  }

  copy(NewarcMateriotechSelection newarcMateriotechSelection) {
    this.id = newarcMateriotechSelection.id;
    this.name = newarcMateriotechSelection.name;
    this.insertTimestamp = newarcMateriotechSelection.insertTimestamp;
    this.modificationTimestamp = newarcMateriotechSelection.modificationTimestamp;
    this.clientId = newarcMateriotechSelection.clientId;
    this.rooms = newarcMateriotechSelection.rooms;
  }

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'insertTimestamp': this.insertTimestamp,
      'modificationTimestamp': this.modificationTimestamp,
      'clientId': this.clientId,
      'rooms': this.rooms.map((e) => e.toMap()).toList(),
    };
  }
}

class NewarcMateriotechRoom {
  String? name;
  String? configuration;
  List<NewarcMateriotechMoodboard> moodboards = [];
  int? insertTimestamp;
  int? modificationTimestamp;

  NewarcMateriotechRoom.empty(){
    this.name = null;
    this.configuration = null;
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.moodboards = [];
  }

  NewarcMateriotechRoom.fromMap(Map<String, dynamic> data){
    this.name = data['name'];
    this.configuration = data['configuration'];
    this.insertTimestamp = data['insertTimestamp'];
    this.modificationTimestamp = data['modificationTimestamp'];
    if (data['moodboards'] != null) {
      for (var i = 0; i < data['moodboards'].length; i++) {
        this.moodboards.add(NewarcMateriotechMoodboard.fromMap(data['moodboards'][i]));
      }
    } else {
      this.moodboards = [];
    }
  }

  Map<String, dynamic> toMap(){
    return {
      'name': this.name,
      'configuration': this.configuration,
      'insertTimestamp': this.insertTimestamp,
      'modificationTimestamp': this.modificationTimestamp,
      'moodboards': this.moodboards.map((e) => e.toMap()).toList(),
    };
  }
}

class NewarcMateriotechMoodboard {
  int? code;
  List materialVariantIds = [];
  int? insertTimestamp;
  int? modificationTimestamp;
  late NewarcSmartMoodboard smartMoodboard;

  NewarcMateriotechMoodboard.empty(){
    this.code = null;
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.materialVariantIds = [];
    this.smartMoodboard = NewarcSmartMoodboard.empty();
  }

  NewarcMateriotechMoodboard.fromMap(Map<String, dynamic> data){
    this.code = data['code'];
    this.insertTimestamp = data['insertTimestamp'];
    this.modificationTimestamp = data['modificationTimestamp'];
    this.materialVariantIds = data['materialVariantIds'] ?? [];
    this.smartMoodboard = data['smartMoodboard'] != null ? NewarcSmartMoodboard.fromMap(data['smartMoodboard']) : NewarcSmartMoodboard.empty();
  }

  Map<String, dynamic> toMap(){
    return {
      'code': code,
      'insertTimestamp': insertTimestamp,
      'modificationTimestamp': modificationTimestamp,
      'materialVariantIds': materialVariantIds,
      'smartMoodboard': smartMoodboard.toMap(),
    };
  }
}

class NewarcSmartMoodboard {
  List materialIds = [];
  late int insertTimestamp;
  String? imageReference;
  bool? generationStatus;
  double? generationDuration;
  Map? generationTokenUsage;
  int? generationTimestamp;

  NewarcSmartMoodboard.empty() {
    this.materialIds = [];
    this.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
    this.imageReference = null;
    this.generationStatus = null;
    this.generationDuration = null;
    this.generationTokenUsage = null;
    this.generationTimestamp = null;
  }

  NewarcSmartMoodboard.fromMap(Map<String, dynamic> data) {
    this.materialIds = data['materialIds'] ?? [];
    this.insertTimestamp = data['insertTimestamp'];
    this.imageReference = data['imageReference'];
    this.generationStatus = data['status'];
    this.generationDuration = data['generationDuration'];
    this.generationTokenUsage = data['generationTokenUsage'];
    this.generationTimestamp = data['generationTimestamp'];
  }

  Map<String, dynamic> toMap() {
    return {
      'materialIds': this.materialIds,
      'insertTimestamp': this.insertTimestamp,
      'imageReference': this.imageReference,
      'status': this.generationStatus,
      'generationDuration': this.generationDuration,
      'generationTokenUsage': this.generationTokenUsage,
      'generationTimestamp': this.generationTimestamp,
    };
  }

  NewarcSmartMoodboard copy() {
    return NewarcSmartMoodboard.fromMap(this.toMap());
  }
}