class NewarcMaterial{
  int? indexPlace;
  String? productName;
  String? supplierName;
  String? group;
  String? transportType; //delivery/collection
  String? measureUnit;
  double? quantity;
  double? price;
  double? cost;
  bool? isAccount;
  double? account;
  
  String? status;
  String? quality;
  String? deposit;

  bool? isPaid;
  int? paidOn;

  bool? isAccountPaid;
  int? accountPaidOn;


  NewarcMaterial(Map<String, dynamic> fixedProperty) {
    this.indexPlace = fixedProperty['indexPlace'];
    this.productName = fixedProperty['productName'];
    this.supplierName = fixedProperty['supplierName'];
    this.group = fixedProperty['group'];
    this.transportType = fixedProperty['transportType'];
    this.measureUnit = fixedProperty['measureUnit'];
    this.quantity = fixedProperty['quantity'];
    this.price = fixedProperty['price'];
    this.cost = fixedProperty['cost'];
    this.isAccount = fixedProperty['isAccount'];
    this.account = fixedProperty['account'];
    this.status = fixedProperty['status'];
    this.quality = fixedProperty['quality'];
    this.deposit = fixedProperty['deposit'];
    this.paidOn = fixedProperty['paidOn'];
    this.isPaid = fixedProperty['isPaid'];
    this.isAccountPaid = fixedProperty['isAccountPaid'];
    this.accountPaidOn = fixedProperty['accountPaidOn'];
    
  }

  NewarcMaterial.empty(){
    this.indexPlace = -1;
    this.productName = '';
    this.supplierName = '';
    this.group = '';
    this.transportType = 'Consegna';
    this.measureUnit = 'mq';
    this.quantity = 0;
    this.price = 0;
    this.cost = 0;
    this.isAccount = false;
    this.account = 0;
    this.status = '';
    this.quality = '';
    this.deposit = '';
    this.paidOn = 0;
    this.isPaid = false;

    this.isAccountPaid = false;
    this.accountPaidOn = 0;
    
  }

  Map<String, dynamic> toMap() {
    return {
      'indexPlace': this.indexPlace,
      'productName': this.productName,
      'supplierName': this.supplierName,
      'group': this.group,
      'transportType': this.transportType,
      'measureUnit': this.measureUnit,
      'quantity': this.quantity,
      'price': this.price,
      'cost': this.cost,
      'isAccount': this.isAccount,
      'account': this.account,
      'status': this.status,
      'quality': this.quality,
      'deposit': this.deposit,
      'paidOn': this.paidOn,
      'isPaid': this.isPaid,
      'isAccountPaid': this.isAccountPaid,
      'accountPaidOn': this.accountPaidOn,
      
    };
  }

  NewarcMaterial.fromDocument(Map<String, dynamic> data, int index) {

    try {
      this.indexPlace = index;
      this.productName = data['productName'] == null ? '' : data['productName'];
      this.supplierName = data['supplierName'] == null ? '' : data['supplierName'];
      this.group = data['group'] == null ? '' : data['group'];
      this.transportType = data['transportType'] == null ? '' : data['transportType'];
      this.measureUnit = data['measureUnit'] == null ? '' : data['measureUnit'];
      this.quantity = data['quantity'] == null ? 0 : data['quantity'];
      this.price = data['price'] == null ? 0 : data['price'];
      this.cost = data['cost'] == null ? 0 : data['cost'];
      this.isAccount = data['isAccount'] == null ? false : data['isAccount'];
      this.account = data['account'] == null ? 0 : data['account'];
      this.status = data['status'] == '' ? '' : data['status'];
      this.quality = data['quality'] == '' ? '' : data['quality'];
      this.deposit = data['deposit'] == '' ? '' : data['deposit'];
      this.paidOn = data['paidOn'] == null ? 0 : data['paidOn'];
      this.isPaid = data['isPaid'] == null ? false : data['isPaid'];

      this.isAccountPaid = data['isAccountPaid'] == null ? false : data['isAccountPaid'];
      this.accountPaidOn = data['accountPaidOn'] == null ? 0 : data['accountPaidOn'];

      // print({'data-->!',this.toMap()}); 
    } catch (e, s) {
      // print({'newarcMateria.dart', e, s});
    }
    
  }

}