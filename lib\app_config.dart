const String FIREBASE_STAGING_API_KEY =
    "AIzaSyBq5h5fMtUIE5i0LKSY8B5y8s6SdG_g1dQ";
const String FIREBASE_STAGING_APP_ID =
    "1:10485153929:web:045ee5077dbb8a66ff0658";
const String FIREBASE_STAGING_MESSAGING_SENDER_ID = "10485153929";
const String FIREBASE_STAGING_STORAGE_BUCKET = "newarc-staging.appspot.com";
const String FIREBASE_STAGING_PROJECT_ID = "newarc-staging";

const String GOOGLE_STAGING_API_KEY = "AIzaSyBq5h5fMtUIE5i0LKSY8B5y8s6SdG_g1dQ";

const String FIREBASE_PRODUCTION_API_KEY =
    "AIzaSyCQonHGTeEdhNQV1XJufEyu-B6rP1msc6Y";
const String FIREBASE_PRODUCTION_APP_ID =
    "1:593015570351:web:bceaad57aee0ca74d8f95a";
const String FIREBASE_PRODUCTION_MESSAGING_SENDER_ID = "593015570351";
const String FIREBASE_PRODUCTION_STORAGE_BUCKET = "newarc.appspot.com";
const String FIREBASE_PRODUCTION_PROJECT_ID = "newarc";

const String GOOGLE_PRODUCTION_API_KEY =
    "AIzaSyCQonHGTeEdhNQV1XJufEyu-B6rP1msc6Y";

String AI_ENDPOINT = "https://newark-api.herokuapp.com";
String COLLECT_VALUATOR_SUBMISSIONS = 'valuatorSubmissions';
String COLLECT_RENOVATION_CONTACTS = 'renovationContacts';
String COLLECT_USERS = 'users';
String COLLECT_SUPPLIERS = 'suppliers';
String COLLECT_AGENCIES = 'agencies';
String COLLECT_VALUATOR_SUBMISSION_COMMENTS = 'valuatorSubmissionComments';
String COLLECT_NEWARC_OPERATIONS = 'newarcOperations';
String COLLECT_PROPOSED_ESTATES = 'proposedEstates';
String COLLECT_NEWARC_HOME = 'newarcHomes';
String COLLECT_NEWARC_PROJECTS = 'newarcProjects';
String COLLECT_PROVISIONAL_ECONOMIC_ACCOUNT = 'provisionalEconomicAccount';
String COLLECT_RENOVATION_QUOTATION = 'renovationQuotation';
String COLLECT_MATERIAL_LIBRARY = 'materialLibrary';
String COLLECT_MOODBOARDS = 'newarcMateriotechSelections';
String COLLECT_IMMAGINA_PROJECTS = 'immaginaProjects';
String COLLECT_IMMAGINA_SUBSCRIPTION = 'subscription';
String COLLECT_WP_FORM_SUBMISSION = 'wpformsSubmissions';
String COLLECT_CATEGORY_ACTIVITY = 'activityCategories';
String COLLECT_RENOVATION_CONTACT_ADDRESS = 'renovationContactAddress';

String COLLECT_NEWARCHMATERIAL = 'newarchMaterial';
String COLLECT_NEWARCMATERIALMANUFACTURER = 'newarcMaterialManufacturer';
String COLLECT_NEWARCMATERIALDIMENSION = 'newarcMaterialDimension';
String COLLECT_NEWARCMATERIALDIMENSIONMETA = 'newarcMaterialDimensionMeta';
String COLLECT_NEWARCMATERIALSUPPLIER = 'newarcMaterialSupplier';
String COLLECT_NEWARCMATERIALVARIANT = 'newarcMaterialVariant';
String COLLECT_NEWARCMATERIALVARIANTSUPPLIERPRICE = 'newarcMaterialVariantSupplierPrice';
String COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION = 'newarcMaterialManufacturerCollection';
String COLLECT_NEWARCMATERIALSUBCATEGORY = 'newarcMaterialSubCategory';
String COLLECT_NEWARCMATERIALVARIANTCOLOR = 'newarcMaterialVariantColor';
String COLLECT_NEWARCMATERIALTIPOLOGIA = 'newarcMaterialTipologia';

String COLLECT_NEWARCADROOMCONFIGURATION  = 'newarcAdRoomConfiguration';
String COLLECT_NEWARCADELEMENTCONFIGURATION  = 'newarcAdElementConfiguration';
String COLLECT_NEWARCADMATERIALCONFIGURATION  = 'newarcAdMaterialConfiguration';
String COLLECT_NEWARCADCONFIGURATION  = 'newarcAdConfiguration';
String COLLECT_NEWARCADOPTIONALCONFIGURATION  = 'newarcAdOptionalConfiguration';
String COLLECT_NEWARCADOPTIONALCATEGORYCONFIGURATION  = 'newarcAdOptionalCategoryConfiguration';

String COLLECT_NEWARC_MATERIOTECH_SELECTIONS = 'newarcMateriotechSelections';

bool isProduction = true;
