class NewarcMaterialDimension {
  String? firebaseId;
  int? insertTimestamp;
  String? uid; //the Firebase Id of the logged in user
  double? length;
  double? width;
  double? height;
  String? dimensionUnit;
  Map? picturePath; //storage location material/firebaseId/image.jpg
  bool? status; /* true if active/false if inactive*/

  //--for MATERIALS - Porte Interne
  String? naMaterialType;



  Map<String, Object?> toMap() {
    return {
      'insertTimestamp': insertTimestamp,
      'uid': uid,
      'length': length,
      'width': width,
      'height': height,
      'dimensionUnit': dimensionUnit,
      'picturePath': picturePath,
      'status': status,
      'naMaterialType': naMaterialType,
    };
  }

  NewarcMaterialDimension.empty() {
    this.firebaseId = '';
    this.length = null;
    this.width = null;
    this.height = null;
    this.insertTimestamp = null;
    this.uid = '';
    this.dimensionUnit = '';
    this.picturePath = {};
    this.status = true;
    this.naMaterialType = '';
  }

  NewarcMaterialDimension.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;

    try {
      this.length = data['length'] is int ? data['length'].toDouble() : data['length'];
      this.width = data['width'] is int ? data['width'].toDouble() : data['width'];
      this.height = data['height'];
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
      this.dimensionUnit = data['dimensionUnit'];
      this.picturePath = data['picturePath'];
      this.status = data['status'];
      this.naMaterialType = data['naMaterialType'];
    } catch (e, s) {
      print({ 'NewarcMaterialDimension Class Error ------->', e, s});
    }
  }
}