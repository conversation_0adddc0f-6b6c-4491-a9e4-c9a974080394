import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:newarc_materiotech/firebase_config.dart';
import 'package:newarc_materiotech/pages/home_materiotech.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter/services.dart';
import 'package:newarc_materiotech/app_config.dart';


Future<void> main() async {

  // Log which environment we're using
  debugPrint('Firebase initialized with ${isProduction ? "PRODUCTION" : "STAGING"} environment');

  try {
    WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
    FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  } catch (e) {
    debugPrint('Error initializing widgets binding: $e');
  }

  try {
    // Initialize Firebase with the appropriate configuration based on isProduction flag
    await Firebase.initializeApp(options: FirebaseConfig.options);
    
  } catch (e) {
    debugPrint('Error initializing Firebase: $e');
  }

  SystemChrome.setPreferredOrientations(
    [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight],
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Newarc Materiotech',
      theme: ThemeData(
        // primaryColorDark: const Color.fromARGB(255, 20, 20, 20),
        primaryColorDark: Colors.black,
        primaryColor: Colors.grey,
        primaryColorLight: Colors.white,
        textSelectionTheme:
          TextSelectionThemeData(
            cursorColor: Colors.white,
            selectionColor: Colors.grey,
            ),

        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}