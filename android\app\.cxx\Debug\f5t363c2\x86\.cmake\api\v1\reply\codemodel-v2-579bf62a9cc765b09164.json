{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/GitHub/newarc-materioteca/android/app/.cxx/Debug/f5t363c2/x86", "source": "C:/Users/<USER>/dev/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}