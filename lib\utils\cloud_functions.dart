import 'package:cloud_functions/cloud_functions.dart';


Future<Map> callSmartMoodboardEndpoint(
  {
    required String materiotechId,
    required String roomName,
    required String moodboardCode,
  }) async {
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'callSmartMoodboardEndpoint',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 10),
      ),
    );
    Map query = {
      'materiotechId': materiotechId,
      'roomName': roomName,
      'moodboardCode': moodboardCode,
    };
    HttpsCallableResult result = await callable.call(query);
    return result.data;
  } catch(e) {
    print(e.toString());
    return {};
  }
}