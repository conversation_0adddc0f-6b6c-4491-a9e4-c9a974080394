// File for Staging Firebase configuration.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Staging [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_staging.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: StagingFirebaseOptions.currentPlatform,
/// );
/// ```
class StagingFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'StagingFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'StagingFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'StagingFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'StagingFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBq5h5fMtUIE5i0LKSY8B5y8s6SdG_g1dQ',
    appId: '1:10485153929:web:045ee5077dbb8a66ff0658',
    messagingSenderId: '10485153929',
    projectId: 'newarc-staging',
    authDomain: 'newarc-staging.firebaseapp.com',
    databaseURL: 'https://newarc-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'newarc-staging.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBUJr5WB7942PoxgkFDk1h2mmOtQO7aodQ',
    appId: '1:10485153929:ios:19f1fec31e1dc2ebff0658',
    messagingSenderId: '10485153929',
    projectId: 'newarc-staging',
    databaseURL: 'https://newarc-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'newarc-staging.appspot.com',
    iosBundleId: 'com.newarc.materioteca',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDWz0RDAOKZpWYlRXxe53YsGqNfscC2hUA',
    appId: '1:10485153929:android:af9333f9e730cbe9ff0658',
    messagingSenderId: '10485153929',
    projectId: 'newarc-staging',
    databaseURL: 'https://newarc-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'newarc-staging.appspot.com',
  );

}