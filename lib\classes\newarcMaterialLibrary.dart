class NewarcMaterialLibrary {
  
  String? id;
  int? counter;
  String? code;
  String? category;
  String? subCategory;
  String? materialName;
  String? supplier;
  
  String? product;
  double? price;
  String? description;
  
  String? image;
  String? imageUrl;
  
  int? created;
  bool? isArchived;
  bool? config;
  bool? isMaterialLibrary;

  String? supplierCode;
  String? supplierMaterial;
  String? dataSheetFile;
  String? coverImage;
  List? applicationImages = [];
  List? format;
  String? thickness;


  NewarcMaterialLibrary(Map<String, dynamic> data) {
    this.counter = data['counter'];
    this.id = data['id'];
    this.code = data['code'];
    this.category = data['category'];
    this.subCategory = data['subCategory'];
    this.materialName = data['materialName'];
    this.supplier = data['supplier'];
    this.product = data['product'];
    this.price = data['price'];
    this.description = data['description'];
    this.image = data['image'] == null ? '' : data['image'];
    this.dataSheetFile = data['dataSheetFile'] == null ? '' : data['dataSheetFile'];
    this.imageUrl = data['imageUrl'] == null ? '' : data['imageUrl'];
    this.created = data['created'];
    this.isArchived = false;
    this.config = data['config']??false;
    this.isMaterialLibrary = data['isMaterialLibrary']??false;
    
    this.supplierCode = data['supplierCode']??'';
    this.supplierMaterial = data['supplierMaterial']??'';
    this.dataSheetFile = data['dataSheetFile']??'';
    this.coverImage = data['coverImage']??'';
    this.applicationImages = data['applicationImages']??[];
    this.format = data['format']??[];
    this.thickness = data['thickness']??'';
    
  }

  NewarcMaterialLibrary.empty(){
    this.counter = 0;
    this.code = '';
    this.category = '';
    this.subCategory = '';
    this.materialName = '';
    this.supplier = '';
    this.product = '';
    this.price = 0.00;
    this.description = '';
    this.image = '';
    this.dataSheetFile = '';
    this.imageUrl = '';
    this.created = 0;
    this.isArchived = false;
    this.isMaterialLibrary = false;
    this.config = false;
    
    this.supplierCode = '';
    this.supplierMaterial = '';
    this.dataSheetFile = '';
    this.coverImage = '';
    this.applicationImages = [];
    this.format = [];
    this.thickness = '';
  }

  Map<String, dynamic> toMap() {
    return {
      'counter': this.counter,
      'code': this.code,
      'category': this.category,
      'subCategory': this.subCategory,
      'materialName': this.materialName,
      'supplier': this.supplier,
      'product': this.product,
      'price': this.price,
      'description': this.description,
      'image': this.image,
      'created': this.created,
      'isArchived': this.isArchived,
      'isMaterialLibrary': this.isMaterialLibrary,
      'config': this.config,
      'supplierCode': this.supplierCode,
      'supplierMaterial': this.supplierMaterial,
      'dataSheetFile': this.dataSheetFile,
      'coverImage': this.coverImage,
      'applicationImages': this.applicationImages,
      'format': this.format,
      'thickness': this.thickness,
      
    };
  }

  NewarcMaterialLibrary.fromDocument(Map<String, dynamic> data, String id) {

    this.id = id;
    this.counter = data['counter'] == null ? 0 : data['counter'];
    this.code = data['code'] == null ? 'no-code' : data['code'];
    this.category = data['category'];
    this.subCategory = data['subCategory'];
    this.materialName = data['materialName'];
    this.product = data['product'] == null ? '' : data['product'];
    this.supplier = data['supplier'];
    this.price = data['price'] == null ? 0 : double.tryParse(data['price'].toString());
    this.description = data['description'];
    this.image = data['image'];
    this.dataSheetFile = data['dataSheetFile'];
    this.created = data['created'];
    this.isArchived = data['isArchived'] == null ? false : data['isArchived'];
    this.isMaterialLibrary = data['isMaterialLibrary'] == null ? false : data['isMaterialLibrary'];
    this.config = data['config'] == null ? false : data['config'];

    this.supplierCode = data['supplierCode'] == null ? '' : data['supplierCode'];
    this.supplierMaterial = data['supplierMaterial'] == null ? '' : data['supplierMaterial'];
    this.dataSheetFile = data['dataSheetFile'] == null ? '' : data['dataSheetFile'];
    this.coverImage = data['coverImage'] == null ? '' : data['coverImage'];
    this.applicationImages = data['applicationImages'] == null ? [] : data['applicationImages'];
    this.format = data['format'] == null ? [] : data['format'];
    this.thickness = data['thickness'] == null ? '' : data['thickness'];
    
  }

}