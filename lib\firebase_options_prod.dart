// File for Production Firebase configuration.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Production [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_prod.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: ProductionFirebaseOptions.currentPlatform,
/// );
/// ```
class ProductionFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'ProductionFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'ProductionFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'ProductionFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'ProductionFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCQonHGTeEdhNQV1XJufEyu-B6rP1msc6Y',
    appId: '1:593015570351:web:35857523106ebb05d8f95a',
    messagingSenderId: '593015570351',
    projectId: 'newarc',
    authDomain: 'newarc.firebaseapp.com',
    storageBucket: 'newarc.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAS4JYdVi-3dyE1gjj3LBQs0gJ6-KmmLmY',
    appId: '1:593015570351:ios:93516b773825da77d8f95a',
    messagingSenderId: '593015570351',
    projectId: 'newarc',
    storageBucket: 'newarc.appspot.com',
    iosBundleId: 'com.newarc.materioteca',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCiBjr-aH1G_Bgo8bSlT9g5yMPTFQdhlDQ',
    appId: '1:593015570351:android:4b48409a9e06b513d8f95a',
    messagingSenderId: '593015570351',
    projectId: 'newarc',
    storageBucket: 'newarc.appspot.com',
  );

}