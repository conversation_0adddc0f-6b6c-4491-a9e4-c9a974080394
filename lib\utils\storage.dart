import 'dart:convert';
import 'dart:math';

import 'package:firebase_storage/firebase_storage.dart';
// Removed image_compression_flutter import due to compatibility issues
import 'package:image_picker/image_picker.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:async' show Future;

Future<UploadTask?> uploadFile(
    String destDirectory, String filename, XFile? file) async {
  if (file == null) {
    return null;
  }

  UploadTask uploadTask;

  // Create a Reference to the file

  // String filename = Timestamp.now().millisecondsSinceEpoch.toString() +'-'+file.name;
  Reference ref =
      FirebaseStorage.instance.ref().child(destDirectory).child(filename);

  final metadata = SettableMetadata(
    contentType: file.mimeType,
    customMetadata: {'picked-file-path': file.path},
  );

  uploadTask = ref.putData(await file.readAsBytes(), metadata);
  uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
    switch (taskSnapshot.state) {
      case TaskState.running:
        final progress =
            100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
        // print("Upload is $progress% complete.");
        break;
      case TaskState.paused:
        // print("Upload is paused.");
        break;
      case TaskState.canceled:
        // print("Upload was canceled");
        break;
      case TaskState.error:
        // Handle unsuccessful uploads
        break;
      case TaskState.success:
        // Handle successful uploads on complete
        // ...
        break;
    }
  });

  return Future.value(uploadTask);
}

Future<void> uploadFileDelayed(
    String destDirectory, String filename, XFile? file) async {
  if (file == null) {
    return;
  }

  // Create a Reference to the file
  Reference ref =
      FirebaseStorage.instance.ref().child(destDirectory).child(filename);

  final metadata = SettableMetadata(
    contentType: file.mimeType,
    customMetadata: {'picked-file-path': file.path},
  );

  // Start the upload task
  UploadTask uploadTask = ref.putData(await file.readAsBytes(), metadata);

  // Wait for the upload to complete
  try {
    await uploadTask;
    final TaskSnapshot taskSnapshot = await uploadTask.whenComplete(() => {});
    if (taskSnapshot.state == TaskState.success) {
      print("Upload complete for $filename");
    }
  } catch (e) {
    print("Upload failed for $filename: $e");
  }
}


Future<UploadTask?> uploadProfilePicture(
    String directory, String docId, String filename, XFile? file) async {
  if (file == null) {
    return null;
  }

  UploadTask uploadTask;

  // Create a Reference to the file

  // String filename = Timestamp.now().millisecondsSinceEpoch.toString() +'-'+file.name;
  Reference ref =
      FirebaseStorage.instance.ref().child(directory + docId).child(filename);

  final metadata = SettableMetadata(
    contentType: file.mimeType,
    customMetadata: {'picked-file-path': file.path},
  );

  uploadTask = ref.putData(await file.readAsBytes(), metadata);
  uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
    switch (taskSnapshot.state) {
      case TaskState.running:
        final progress =
            100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
        // print("Upload is $progress% complete.");
        break;
      case TaskState.paused:
        // print("Upload is paused.");
        break;
      case TaskState.canceled:
        // print("Upload was canceled");
        break;
      case TaskState.error:
        // Handle unsuccessful uploads
        break;
      case TaskState.success:
        // Handle successful uploads on complete
        // ...
        break;
    }
  });

  return Future.value(uploadTask);
}

Future<UploadTask?> uploadAgencyProfilePicture(
    String docId, String filename, XFile? file) async {
  if (file == null) {
    return null;
  }

  UploadTask uploadTask;

  // Create a Reference to the file

  // String filename = Timestamp.now().millisecondsSinceEpoch.toString() +'-'+file.name;
  Reference ref =
      FirebaseStorage.instance.ref().child('agencies/' + docId).child(filename);

  final metadata = SettableMetadata(
    contentType: file.mimeType,
    customMetadata: {'picked-file-path': file.path},
  );

  uploadTask = ref.putData(await file.readAsBytes(), metadata);
  uploadTask.snapshotEvents.listen((TaskSnapshot taskSnapshot) {
    switch (taskSnapshot.state) {
      case TaskState.running:
        final progress =
            100.0 * (taskSnapshot.bytesTransferred / taskSnapshot.totalBytes);
        // print("Upload is $progress% complete.");
        break;
      case TaskState.paused:
        // print("Upload is paused.");
        break;
      case TaskState.canceled:
        // print("Upload was canceled");
        break;
      case TaskState.error:
        // Handle unsuccessful uploads
        break;
      case TaskState.success:
        // Handle successful uploads on complete
        // ...
        break;
    }
  });

  return Future.value(uploadTask);
}

Future<String> printUrl(directory, docId, filename) async {

  // print({directory, docId, filename});
  if( directory == '' || filename == '' ) return '';
  try {
    Reference ref = FirebaseStorage.instance
      .ref()
      .child(directory + docId.toString() + "/" + filename.toString());
    String url = await ref.getDownloadURL().then((value) {
      return value;
    });
    return url;
  } catch (e,s) {
    print({ 'printUrl', directory, docId, filename, e,s});
    return '';
  }

}

Future<String> agencyProfileUrl(docId, filename) async {
  try {
    Reference ref = FirebaseStorage.instance
        .ref()
        .child("agencies/" + docId.toString() + "/" + filename.toString());
    String url = await ref.getDownloadURL().then((value) {
      return value;
    });
    return url;
  } catch (e) {
    return '';
  }
}


Future<String> getDownloadLink(storage,docId, filename) async {
  try {
    Reference ref = FirebaseStorage.instance
        .ref()
        .child("$storage/" + docId.toString() + "/" + filename.toString());
    String url = await ref.getDownloadURL().then((value) {
      return value;
    });
    return url;
  } catch (e) {
    return '';
  }
}

Future<String> downloadLink(Reference ref) async {
  String link = await ref.getDownloadURL();

  return link;
}

Future<bool> deleteFile(String directory, String filename) async {
  try {
    Reference ref =
        FirebaseStorage.instance.ref().child(directory + filename.toString());
    await ref.delete();
    return true;
  } catch (e) {
    return false;
  }
}

deleteDirectory(String directoryPath) async {
  await FirebaseStorage.instance.ref(directoryPath).listAll().then((value) {
    value.items.forEach((element) {
      FirebaseStorage.instance.ref(element.fullPath).delete();
    });
  });
}

// Simplified version without image_compression_flutter
Future<XFile> handleCompressImage(XFile image) async {
  print("Image compression is disabled due to compatibility issues");
  // Simply return the original image for now
  return image;

  // Original implementation using image_compression_flutter:
  /*
  print("Compressing image");

  ImageFile input = ImageFile(
      filePath: image.path,
      rawBytes: await image.readAsBytes()); // set the input image file

  Configuration config = Configuration(
    outputType: ImageOutputType.webpThenJpg,
    // can only be true for Android and iOS while using ImageOutputType.jpg or ImageOutputType.pngÏ
    useJpgPngNativeCompressor: false,
    // set quality between 0-100
    quality: 60,
  );

  final param = ImageFileConfiguration(input: input, config: config);
  final output = await compressor.compress(param);
  return output;
  */
}

getFileUrl(filename) {
  Random random = new Random();
  String randomToken = (random.nextInt(1000) + 1000).toString() +
      '-' +
      (random.nextInt(1000) + 1000).toString() +
      '-' +
      (random.nextInt(1000) + 1000).toString() +
      '-' +
      (random.nextInt(1000) + 1000).toString();
  String fs_url = 'https://firebasestorage.googleapis.com/v0/b/' +
      FirebaseStorage.instance.bucket +
      '/o/${filename}?alt=media&token=' +
      randomToken;

  return fs_url;
}
