import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationRenovationApp {
  int? created;
  String? jobName;
  String? type;
  bool? read;

  NotificationRenovationApp.fromDocument(Map<String, dynamic> data) {
    this.created = data['created'] ?? "";
    this.jobName = data['jobName'] ?? "";
    this.type = data['type'] ?? "";
    this.read = data['read'] ?? false;
  }

  NotificationRenovationApp.empty() {
    this.created = Timestamp.now().millisecondsSinceEpoch;
    this.jobName = "";
    this.type = "";
    this.read = false;
  }

  Map<String, dynamic> toMap() {
    return {
      'created': this.created,
      'jobName': this.jobName,
      'type': this.type,
      'read': this.read,
    };
  }
}