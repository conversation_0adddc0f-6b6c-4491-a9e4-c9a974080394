class Agreement{
  
  int? indexPlace;
  String? uid;
  List? images;
  String? virtualTourLink;
  int? date;
  
  Agreement(Map<String, dynamic> fixedProperty) {
    this.indexPlace = fixedProperty['indexPlace'];
    this.virtualTourLink = fixedProperty['virtualTourLink'];
    this.uid = fixedProperty['uid'];
    this.date = fixedProperty['date'] == '' ? '' : fixedProperty['date'];
    this.images = fixedProperty['images'] == '' ? [] : fixedProperty['images'];
    
  }

  Agreement.empty(){
    this.indexPlace = -1;
    this.virtualTourLink = '';
    this.uid = '';
    this.date = 0;
    this.images = [];
    
  }

  Map<String, dynamic> toMap() {
    return {
      'indexPlace': this.indexPlace,
      'virtualTourLink': this.virtualTourLink,
      'uid': this.uid,
      'date': this.date,
      'images': this.images
    };
  }

  Agreement.fromDocument(Map<String, dynamic> data, int index) {

    try {
      // print({ 'data-->', data});
      this.indexPlace = index;
      this.virtualTourLink = data['virtualTourLink'];
      this.uid = data['uid'];
      this.date = data['date'] == '' ? 0 : data['date'];
      this.images = data['images'] == '' ? [] : data['images'];
      
    } catch (e, s) {
      print({'agreement.dart', e, s});
    }
    
  }

}

