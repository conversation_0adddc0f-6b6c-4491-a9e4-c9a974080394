class NewarcMaterialManufacturerCollection{
  String? firebaseId;
  String? name; // Collection Name
  int? insertTimestamp;
  int? modificationTimestamp;
  String? creationUid; //the Firebase Id of the logged in user
  String? modificationUid; //the Firebase Id of the logged in user
  String? description;
  bool? isArchived; //default to false
  Map? filePath;
  String? newarcManufacturerId;
  String? collectionFirebaseId;
  int? codeCounter;

  Map<String, Object?> toMap() {
    return {
      'name': name,
      'insertTimestamp': insertTimestamp,
      'modificationTimestamp': modificationTimestamp,
      'creationUid': creationUid,
      'modificationUid': modificationUid,
      'description': description,
      'isArchived': isArchived,
      'filePath': filePath,
      'newarcManufacturerId': newarcManufacturerId,
      'collectionFirebaseId': collectionFirebaseId,
      'codeCounter': codeCounter,
    };
  }

  NewarcMaterialManufacturerCollection.empty() {
    this.firebaseId = '';
    this.name = '';
    this.insertTimestamp = null;
    this.modificationTimestamp = null;
    this.creationUid = '';
    this.modificationUid = '';
    this.description = '';
    this.isArchived = false;
    this.filePath = {};
    this.newarcManufacturerId = '';
    this.collectionFirebaseId = '';
    this.codeCounter = 0;
  }

  NewarcMaterialManufacturerCollection.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.name = data['name'];
      this.insertTimestamp = data['insertTimestamp'];
      this.modificationTimestamp = data['modificationTimestamp'];
      this.creationUid = data['creationUid'];
      this.modificationUid = data['modificationUid'];
      this.description = data['description'];
      this.isArchived = data['isArchived'];
      this.filePath = data['filePath'];
      this.newarcManufacturerId = data['newarcManufacturerId'];
      this.collectionFirebaseId = data['collectionFirebaseId'];
      this.codeCounter = data['codeCounter'];
    } catch (e, s) {
      print({ 'NewarcProductCollection Class Error ------->', e, s});
    }
  }
}