import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';


class SiNoToggleButton extends StatefulWidget {
  final bool? startingState;
  final Function(bool)? onStateChanged;
  const SiNoToggleButton ({ 
    Key? key, 
    this.startingState, 
    this.onStateChanged,
    }): super(key: key);
  @override
  _SiNoToggleButtonState createState() => _SiNoToggleButtonState();
}

class _SiNoToggleButtonState extends State<SiNoToggleButton> {
  late bool _isSelected;
  @override
  void initState() {
    super.initState();
    setState(() {
      _isSelected = widget.startingState ?? false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return 
    Row(
      mainAxisAlignment: MainAxisAlignment.start,
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(width: 5,),
        GestureDetector(
          onTap: () {
            setState(() {
            _isSelected = true;
            widget.onStateChanged?.call(_isSelected);
            });
          },
          child: 
            Container(
              width: 15,
              height: 15,
              decoration: 
                BoxDecoration(
                  shape: BoxShape.circle, 
                  color: _isSelected ? Theme.of(context).primaryColor: Theme.of(context).unselectedWidgetColor,
                  border: Border.all(color: Theme.of(context).primaryColorLight)
                ),
                child: 
                  _isSelected 
                  ? Padding(
                    padding: EdgeInsets.all(1),
                    child: SvgPicture.asset(
                      'assets/icons/check.svg', 
                      color: Theme.of(context).unselectedWidgetColor
                    )
                  ) 
                  : null,
            )
        ),
        const SizedBox(width: 10,),
        Text('Si',
          style: TextStyle(
            fontFamily: 'Raleway-500',
            fontSize: 15,
            color: Theme.of(context).primaryColorLight,),
        ),
        const SizedBox(width: 30,),
        GestureDetector(
          onTap: () {
            setState(() {
            _isSelected = false;
            widget.onStateChanged?.call(_isSelected);
            });
          },
          child: 
            Container(
              width: 15,
              height: 15,
              decoration: 
                BoxDecoration(
                  shape: BoxShape.circle, 
                  color: _isSelected ? Theme.of(context).unselectedWidgetColor: Theme.of(context).primaryColor,
                  border: Border.all(color: Theme.of(context).primaryColorLight)
                ),
              child: 
                !_isSelected 
                ? Padding(
                  padding: EdgeInsets.all(1),
                  child: SvgPicture.asset(
                    'assets/icons/check.svg', 
                    color: Theme.of(context).unselectedWidgetColor
                  )
                ) 
                : null,
            )
        ),
        const SizedBox(width: 10,),
        Text('No',
          style: TextStyle(
            fontFamily: 'Raleway-500',
            fontSize: 15,
            color: Theme.of(context).primaryColorLight,),
        ),
      ],
    );
  }
}