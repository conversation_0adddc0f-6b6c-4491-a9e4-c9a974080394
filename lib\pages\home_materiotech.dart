import 'dart:math';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_materiotech/app_config.dart' as appConfig;
import 'package:newarc_materiotech/app_const.dart' as appConst;
import 'package:newarc_materiotech/classes/NAMaterial.dart';
import 'package:newarc_materiotech/classes/NewarcMaterialVariant.dart';
import 'package:newarc_materiotech/classes/NewarcMaterialManufacturer.dart';
import 'package:newarc_materiotech/classes/NewarcMaterialManufacturerCollection.dart';
import 'package:newarc_materiotech/classes/NewarcMaterialDimension.dart';
import 'package:newarc_materiotech/classes/NewarcMaterialDimensionMeta.dart';
import 'package:newarc_materiotech/classes/newarcProject.dart';
import 'package:newarc_materiotech/utils/firestore.dart';
import 'package:newarc_materiotech/utils/inputFormatters.dart';
import 'package:newarc_materiotech/utils/various.dart';
import 'package:newarc_materiotech/utils/cloud_functions.dart';
import 'package:newarc_materiotech/widgets/UI/address_completion_google.dart';
import 'package:newarc_materiotech/widgets/UI/select_box.dart';
import 'package:newarc_materiotech/widgets/UI/si_no_button.dart';
import 'package:newarc_materiotech/widgets/UI/dropdown_search.dart';
import 'package:newarc_materiotech/widgets/UI/base_newarc_popup.dart';
import 'package:newarc_materiotech/widgets/UI/form_label.dart';
import 'package:newarc_materiotech/widgets/UI/custom_textformfield.dart';
import 'package:newarc_materiotech/widgets/UI/image_select_box.dart';
import 'package:newarc_materiotech/widgets/UI/newarc_material_icon.dart';
import 'package:newarc_materiotech/classes/user.dart';
import 'package:newarc_materiotech/classes/baseAddressInfo.dart';
import 'package:newarc_materiotech/classes/basePersonInfo.dart';
import 'package:newarc_materiotech/classes/renovationContact.dart';
import 'package:newarc_materiotech/classes/newarcMateriotechSelection.dart';
import 'package:newarc_materiotech/classes/newarcMaterialLibrary.dart';
import 'package:newarc_materiotech/widgets/UI/custom_progress_indicator.dart';



class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  bool _loading = true;
  bool isMateriotechSelected = false;
  bool isMateriotechNew = true;
  UserCredential? currentUser;
  String selectedView = 'select_project';
  // select materiotech project
  TextEditingController filterClient = TextEditingController();
  TextEditingController filterMateriotech = TextEditingController();
  RenovationContact selectedClient = RenovationContact.empty();
  NewarcMateriotechSelection selectedMateriotech = NewarcMateriotechSelection.empty();
  List<RenovationContact> clientsList = [];
  List<NewarcMateriotechSelection> materiotechsList = [];
  // add client popup
  bool loadingRenovators = true;
  List<NewarcUser> renovatorsList = [];
  TextEditingController contactNameController = TextEditingController();
  TextEditingController contactSurnameController = TextEditingController();
  TextEditingController contactEmailController = TextEditingController();
  TextEditingController contactPhoneController = TextEditingController();
  TextEditingController renovatorController = TextEditingController();
  BaseAddressInfo contactAddressInfo = BaseAddressInfo.empty();
  // MATERIOTECH DASHBOARD
  NewarcMateriotechRoom selectedRoom = NewarcMateriotechRoom.empty();
  NewarcMateriotechMoodboard selectedMoodboard = NewarcMateriotechMoodboard.empty();
  // add materials to room
  TextEditingController filterCategoria = TextEditingController();
  TextEditingController filterProduttore = TextEditingController();
  TextEditingController filterCollezione = TextEditingController();
  TextEditingController filterProdotto = TextEditingController();
  String selectedCategoria = '';
  NewarcMaterialManufacturer? selectedProduttore;
  NewarcMaterialManufacturerCollection? selectedCollezione;
  NewarcMaterialVariantExtension? selectedProdotto;
  List<NAMaterial> materialsList = [];
  List<NewarcMaterialManufacturer> manufacturersList = [];
  List<NewarcMaterialManufacturerCollection> collectionsList = [];
  List<NewarcMaterialVariantExtension> variantsList = [];
  // add room popup
  TextEditingController roomNameController = TextEditingController();
  TextEditingController roomConfigController = TextEditingController();
  String formProgressMessage = '';


  @override
  void initState() {
    super.initState();
    _loginAndFetchData();
    FlutterNativeSplash.remove();
  }

  void _loginAndFetchData() async {
  try {
    UserCredential userCredential = await FirebaseAuth.instance.signInAnonymously();
    setState(() {
      currentUser = userCredential;
    });

    _fetchData();

  } catch (e) {
    print("Error in _loginAndFetchData: $e");
  }
}

  _fetchData() async {
    setState(() => _loading = true,);
    await _fetchRenovators();
    await _fetchClients();
    await _fetchMaterioteques();
    await _fetchNAMaterials();
    setState(() => _loading = false,);
  }

  _fetchClients() async {
    List<RenovationContact> _clients = [];

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery = FirebaseFirestore.instance.collection(appConfig.COLLECT_RENOVATION_CONTACTS);
    collectionSnapshot = await collectionSnapshotQuery.get();
    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          RenovationContact _tmp = RenovationContact.fromDocument(element.data(), element.id);
          _clients.add(_tmp);
        } catch (e) {
          // print("error in document ${element.id}");
          // print(e);
        }
      }
    }
    setState(() {
      clientsList = _clients;
    });
  }

  _fetchRenovators() async {
    List<NewarcUser> _renovators = [];
    setState(() {
      loadingRenovators = true;
    });

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery =
        FirebaseFirestore.instance.collection(appConfig.COLLECT_USERS);
    collectionSnapshot = await collectionSnapshotQuery
        .where('type', isEqualTo: 'newarc')
        .where('role', isEqualTo: 'renovator')
        .get();

    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NewarcUser _tmp = NewarcUser.fromDocument(element.data(), element.id);
          _renovators.add(_tmp);
        } catch (e) {
          // print("error in document ${element.id}");
          // print(e);
        }
      }
    }
    setState(() {
      renovatorsList = _renovators;
      loadingRenovators = false;
    });
  }

  _fetchMaterioteques() async {
    List<NewarcMateriotechSelection> _materiotechList = [];

    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery =
        FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS);

    collectionSnapshot = await collectionSnapshotQuery
        .orderBy('insertTimestamp', descending: false)
        .get();

    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NewarcMateriotechSelection _tmp = NewarcMateriotechSelection.fromDocument(element.data(), element.id);
          _materiotechList.add(_tmp);
        } catch (e) {
          // print("error in document ${element.id}");
          // print(e);
        }
      }
    }
    setState(() {
      materiotechsList = _materiotechList;
    });
  }

  _fetchNAMaterials() async {
    List<NAMaterial> _materialsList = [];
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery =
        FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCHMATERIAL);
      collectionSnapshot = await collectionSnapshotQuery
      .where("isMateriotechAvailable", isEqualTo: true)
      .get();


    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NAMaterial _tmp = NAMaterial.fromDocument(element.data(), element.id);
          _materialsList.add(_tmp);
        } catch (e) {
          // print("error in document ${element.id}");
          // print(e);
        }
      }
    }
    setState(() {
      materialsList = _materialsList;
    });
  }

  _fetchNewarcMaterialManufacturers(String category) async {
    List<NewarcMaterialManufacturer> _manufacturersList = [];
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery =
        FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURER);
      collectionSnapshot = await collectionSnapshotQuery
      .get();


    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NewarcMaterialManufacturer _tmp = NewarcMaterialManufacturer.fromDocument(element.data(), element.id);
          if (materialsList.where((item) => item.materialType == category)
                            .map((elem) => elem.newarcManufacturerID)
                            .toList()
                            .contains(_tmp.firebaseId)){
            _manufacturersList.add(_tmp);
          }
        } catch (e) {
          // print("error in document ${element.id}");
          // print(e);
        }
      }
    }
    setState(() {
      manufacturersList = _manufacturersList;
    });
  }

  _fetchNewarcMaterialManufacturerCollections(NewarcMaterialManufacturer manufacturer) async {
    List<NewarcMaterialManufacturerCollection> _collectionsList = [];
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;

    collectionSnapshotQuery =
        FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION);
      collectionSnapshot = await collectionSnapshotQuery
      .where('newarcManufacturerId', isEqualTo: manufacturer.firebaseId)
      .get();

    if (collectionSnapshot.docs.length > 0) {
      for (var element in collectionSnapshot.docs) {
        try {
          NewarcMaterialManufacturerCollection _tmp = NewarcMaterialManufacturerCollection.fromDocument(element.data(), element.id);
          if (materialsList.where((item) => (item.materialType == selectedCategoria)&&(item.newarcManufacturerID == manufacturer.firebaseId))
                            .map((elem) => elem.newarcProductCollectionID)
                            .toList()
                            .contains(_tmp.firebaseId)){
            _collectionsList.add(_tmp);
          }
        } catch (e) {
          // print("error in document ${element.id}");
          // print(e);
        }
      }
    }
    setState(() {
      collectionsList = _collectionsList;
    });
  }

  _fetchNewarcMaterialVariants(NewarcMaterialManufacturerCollection collection) async {
    List<NewarcMaterialVariantExtension> _variantsList = [];
    QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
    Query<Map<String, dynamic>> collectionSnapshotQuery;
    if (selectedCategoria == 'tinte') {
      _variantsList = materialsList.where((item) => (item.materialType == selectedCategoria) && (item.newarcProductCollectionID == collection.firebaseId))
                            .map((elem){
                              NewarcMaterialVariantExtension tmp = NewarcMaterialVariantExtension.empty();
                              tmp.naMaterial = elem;
                              tmp.firebaseId = elem.firebaseId;
                              return tmp;
                            })
                            .toList();
      setState(() {
        variantsList = _variantsList;
      });
    } else {
      collectionSnapshotQuery =
          FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCMATERIALVARIANT);
        collectionSnapshot = await collectionSnapshotQuery
        .get();

      if (collectionSnapshot.docs.isNotEmpty) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcMaterialVariantExtension _tmp = NewarcMaterialVariantExtension.fromDocument(element.data(), element.id);
            if (materialsList.where((item) => (item.newarcProductCollectionID == collection.firebaseId)&&(item.materialType == selectedCategoria))
                              .map((elem) => elem.firebaseId)
                              .toList()
                              .contains(_tmp.naMaterialId)){
              DocumentSnapshot<Map<String, dynamic>> materialDimSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCMATERIALDIMENSION).doc(_tmp.newarcMaterialDimensionID!).get();
              DocumentSnapshot<Map<String, dynamic>> materialDimMetaSnapshot = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARCMATERIALDIMENSIONMETA).doc(_tmp.newarcMaterialDimensionMetaID!).get();
              _tmp.naMaterial = materialsList.where((item) => item.firebaseId == _tmp.naMaterialId).first;
              _tmp.naMaterialDimension = NewarcMaterialDimension.fromDocument(materialDimSnapshot.data()!, materialDimSnapshot.id);
              _tmp.naMaterialDimensionMeta = NewarcMaterialDimensionMeta.fromDocument(materialDimMetaSnapshot.data()!, materialDimMetaSnapshot.id);
              _variantsList.add(_tmp);
            }
          } catch (e) {
            // print("error in document ${element.id}");
            // print(e);
          }
        }
      }
    }
    setState(() {
      variantsList = _variantsList;
    });
  }

  // Fetch a material variant and its related objects from Firestore
  Future<NewarcMaterialVariantExtension> fetchMaterialVariantWithRelatedData(String variantId) async {
    try {
      // Fetch the NewarcMaterialVariant from Firestore
      DocumentSnapshot<Map<String, dynamic>> variantDoc = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALVARIANT)
          .doc(variantId)
          .get();
      
      // If no materialVariant is found for ID (eg "tinte" category has no variants) return empty object
      NewarcMaterialVariant variant = NewarcMaterialVariant.empty();
      variant.firebaseId = variantId;
      if (variantDoc.exists) {
        // Create the variant object from document
        variant = NewarcMaterialVariant.fromDocument(variantDoc.data()!, variantDoc.id);
        // throw Exception('Material variant not found with ID: $variantId');
      }

      // Fetch the NAMaterial using variant.naMaterialId if variant was found
      DocumentSnapshot<Map<String, dynamic>> materialDoc;
      if (variantDoc.exists) {
        materialDoc = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCHMATERIAL)
            .doc(variant.naMaterialId)
            .get();
      // if no variant was found fetch material using variantId (eg case categoria "tinte")
      } else {
        materialDoc = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCHMATERIAL)
            .doc(variantId)
            .get();
      }

      if (!materialDoc.exists) {
        throw Exception('Material not found with ID: ${variant.naMaterialId}');
      }

      NAMaterial material = NAMaterial.fromDocument(materialDoc.data()!, materialDoc.id);

      // Fetch the newarcMaterialManufacturerCollection using newarcProductCollectionID
      DocumentSnapshot<Map<String, dynamic>> collectionDoc = await FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCMATERIALMANUFACTURERCOLLECTION)
          .doc(material.newarcProductCollectionID)
          .get();

      if (!collectionDoc.exists) {
        throw Exception('Material not found with ID: ${variant.naMaterialId}');
      }

      NewarcMaterialManufacturerCollection collection = NewarcMaterialManufacturerCollection.fromDocument(collectionDoc.data()!, collectionDoc.id);

      // If no variant was found for ID (eg "tinte" category has no variants) return empty dimension, dimensionMeta nad allVariants objects
      NewarcMaterialDimension dimension = NewarcMaterialDimension.empty();
      NewarcMaterialDimensionMeta dimensionMeta = NewarcMaterialDimensionMeta.empty();
      List<NewarcMaterialVariantExtension> allVariants = [];
      if (variantDoc.exists) {
        // Fetch the NewarcMaterialDimension using newarcMaterialDimensionID
        DocumentSnapshot<Map<String, dynamic>> dimensionDoc = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCMATERIALDIMENSION)
            .doc(variant.newarcMaterialDimensionID)
            .get();

        if (!dimensionDoc.exists) {
          throw Exception('Material dimension not found with ID: ${variant.newarcMaterialDimensionID}');
        }

        dimension = NewarcMaterialDimension.fromDocument(dimensionDoc.data()!, dimensionDoc.id);

        // Fetch the NewarcMaterialDimensionMeta using newarcMaterialDimensionMetaID
        DocumentSnapshot<Map<String, dynamic>> dimensionMetaDoc = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCMATERIALDIMENSIONMETA)
            .doc(variant.newarcMaterialDimensionMetaID)
            .get();

        if (!dimensionMetaDoc.exists) {
          throw Exception('Material dimension meta not found with ID: ${variant.newarcMaterialDimensionMetaID}');
        }

        dimensionMeta = NewarcMaterialDimensionMeta.fromDocument(dimensionMetaDoc.data()!, dimensionMetaDoc.id);

        // Fetch other formats and thicknesses
        QuerySnapshot<Map<String, dynamic>> allVariantsSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCMATERIALVARIANT)
            .where('naMaterialId', isEqualTo: variant.naMaterialId)
            .get();
        for (var doc in allVariantsSnapshot.docs) {
          NewarcMaterialVariantExtension otherVariant = NewarcMaterialVariantExtension.fromDocument(doc.data(), doc.id);
          DocumentSnapshot<Map<String, dynamic>> otherDimensionSnapshot = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCMATERIALDIMENSION)
            .doc(otherVariant.newarcMaterialDimensionID)
            .get();
          if (!otherDimensionSnapshot.exists) {
            throw Exception('Material dimension not found with ID: ${variant.newarcMaterialDimensionID}');
          }
          otherVariant.naMaterialDimension = NewarcMaterialDimension.fromDocument(otherDimensionSnapshot.data()!, otherDimensionSnapshot.id);

          DocumentSnapshot<Map<String, dynamic>> otherDimensionMetaDoc = await FirebaseFirestore.instance
            .collection(appConfig.COLLECT_NEWARCMATERIALDIMENSIONMETA)
            .doc(otherVariant.newarcMaterialDimensionMetaID)
            .get();
          if (!otherDimensionMetaDoc.exists) {
            throw Exception('Material dimension meta not found with ID: ${variant.newarcMaterialDimensionMetaID}');
          }
          otherVariant.naMaterialDimensionMeta = NewarcMaterialDimensionMeta.fromDocument(otherDimensionMetaDoc.data()!, otherDimensionMetaDoc.id);
          otherVariant.naMaterialCollection = collection;
          otherVariant.naMaterial = material;
          allVariants.add(otherVariant);
        }
      }

      // Create and return the NewarcMaterialVariantExtension with all the fetched data
      return NewarcMaterialVariantExtension.withRelatedData(
        variant,
        material,
        collection,
        dimension,
        dimensionMeta,
        allVariants,
      );
    } catch (e) {
      print('Error fetching material variant data: $e');
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Theme.of(context).primaryColorDark,
      body: LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
        double marginSpacing = MediaQuery.of(context).size.height * .03; // padding on top and bottom margin (x2)
        double headerSpacing = !isMateriotechSelected ? MediaQuery.of(context).size.height * .07 : MediaQuery.of(context).size.height * .05;
        double footerSpacing = MediaQuery.of(context).size.height * .08;
        double viewSpacing = !isMateriotechSelected ? MediaQuery.of(context).size.height * .77 : MediaQuery.of(context).size.height * .81;
        return
          Center(
            child:
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(height: marginSpacing),
                  _buildHeader(headerSpacing),
                  SizedBox(
                    height: viewSpacing,
                    child: selectView(viewSpacing, headerSpacing, footerSpacing),
                  ),
                  _buildFooter(footerSpacing),
                  SizedBox(height: marginSpacing),
                ],
              )
          );
      }),
    );
  }

  _backButtonSetStateFunction(){
    setState(() {
      selectedView = 'select_project';
      isMateriotechSelected = false;
      isMateriotechNew = true;
      filterClient.clear();
      selectedClient = RenovationContact.empty();
      filterMateriotech.clear();
      selectedMateriotech = NewarcMateriotechSelection.empty();
      _fetchData();
      filterCategoria.clear();
      selectedCategoria = '';
      filterProduttore.clear();
      selectedProduttore = null;
      filterCollezione.clear();
      selectedCollezione = null;
      filterProdotto.clear();
      selectedProdotto = null;
      materialsList = [];
      renovatorsList = [];
      contactNameController.clear();
      contactSurnameController.clear();
      contactEmailController.clear();
      contactPhoneController.clear();
      renovatorController.clear();
      contactAddressInfo = BaseAddressInfo.empty();
      roomNameController.clear();
      roomConfigController.clear();
      selectedRoom = NewarcMateriotechRoom.empty();
      selectedMoodboard = NewarcMateriotechMoodboard.empty();
      formProgressMessage = '';
    });
  }

  Widget _buildHeader(headerHeight){
    if (_loading) {
      return
        SizedBox(
          height: headerHeight,
        );
    } else if (selectedView == 'dashboard_materioteca') {
      return SizedBox.shrink();
    }
    return SizedBox(
      height: headerHeight,
      child: Image.asset(
        'assets/newarc-materiotech-white.png',
        fit: BoxFit.contain,
      ),
    );
  }

  Widget _buildFooter(footerHeight){
    if (_loading) {
      return
        SizedBox(
          height: footerHeight,
        );
    }
    else if (selectedView != 'select_project'){
      return
        SizedBox(
          height: footerHeight,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              SizedBox(
                width: !isMateriotechSelected ? MediaQuery.of(context).size.width * .40 : MediaQuery.of(context).size.width * .30,
                height: footerHeight,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 30),
                      child: !isMateriotechSelected
                      ? SizedBox(
                        height: footerHeight,
                        width: footerHeight,
                        child: ElevatedButton(
                          onPressed: () =>_backButtonSetStateFunction(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xff1f1f1f),
                            // foregroundColor: Theme.of(context).primaryColor,
                            // side: BorderSide(color: Theme.of(context).primaryColorLight),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            padding: EdgeInsets.all(0)
                          ),
                          child: Center(
                            child:Transform.rotate(
                              angle: pi,
                              child: SvgPicture.asset(
                                'assets/icons/arrow.svg',
                                height: footerHeight*.3,
                                width: footerHeight*.3,
                                color: Color(0xff9f9f9f),
                                // color: Theme.of(context).primaryColorLight,
                                fit: BoxFit.contain,
                              ),
                            )
                          )
                        )
                      )
                      : SizedBox(
                        height: footerHeight*.9,
                        width: footerHeight*.9,
                        child: ElevatedButton(
                          onPressed: () =>_backButtonSetStateFunction(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColorDark,
                            // foregroundColor: Theme.of(context).primaryColor,
                            side: const BorderSide(color: Color(0xff9f9f9f)),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            padding: const EdgeInsets.only(left: 15, right: 15)
                          ),
                          child: Transform.rotate(
                            angle: pi,
                            child: SvgPicture.asset(
                              'assets/icons/arrow.svg',
                              height: footerHeight*.3,
                              width: footerHeight*.3,
                              color: const Color(0xff9f9f9f),
                              // color: Theme.of(context).primaryColor,
                              fit: BoxFit.contain,
                            ),
                          )
                        )
                      )
                    )
                  ]
                )
              ),
              SizedBox(
                height: footerHeight,
                width: !isMateriotechSelected ? MediaQuery.of(context).size.width * .20 : MediaQuery.of(context).size.width * .40,
                child:
                  !isMateriotechSelected
                  ? ElevatedButton(
                    onPressed: () async {
                      if (selectedView == 'existing_project') {
                        if (selectedMateriotech.id != '' && selectedClient.id != '' && selectedMateriotech.id != null) {
                          setState(() {
                            isMateriotechSelected = true;
                            selectedRoom = selectedMateriotech.rooms.isNotEmpty ? selectedMateriotech.rooms.first : NewarcMateriotechRoom.empty();
                            selectedMoodboard = selectedRoom.moodboards.isNotEmpty ? selectedRoom.moodboards.first : NewarcMateriotechMoodboard.empty();
                            selectedView = 'dashboard_materioteca';
                          });
                        }
                      }
                      else if (selectedView == 'new_project') {
                        if (selectedClient.id != ""){
                          setState(() {
                            _loading = true;
                          });
                          NewarcMateriotechSelection _selectedMateriotech = NewarcMateriotechSelection.empty();
                          _selectedMateriotech.clientId = selectedClient.id;
                          // _selectedMateriotech.name = selectedClient.addressInfo?.toShortAddress() ?? selectedClient.streetAddress;

                          DocumentReference savedId = await FirebaseFirestore.instance
                            .collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS)
                            .add(_selectedMateriotech.toMap());

                          _selectedMateriotech.id = savedId.id;

                          setState(() {
                            selectedMateriotech = _selectedMateriotech;
                            isMateriotechSelected = true;
                            selectedView = 'dashboard_materioteca';
                            _loading = false;
                          });
                        }
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColorLight,
                      foregroundColor: Theme.of(context).primaryColorDark,
                      side: BorderSide(color: Theme.of(context).primaryColorLight),
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                    ),
                    child:
                      Text(
                        'Avanti',
                        selectionColor: Theme.of(context).primaryColorDark,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'Raleway',
                        ),
                      ),
                  )
                  : Container(
                    decoration: BoxDecoration(
                      color: Colors.black87,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Color(0xff9f9f9f), width: 1)
                    ),
                    padding: EdgeInsets.all(10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'INDIRIZZO',
                                style:
                                  TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Raleway',
                                  ),
                                textAlign: TextAlign.left,
                                ),
                                Text(
                                  selectedMateriotech.name == '' ? 'NoName' : selectedMateriotech.name!,
                                  style:
                                    TextStyle(
                                      color: Theme.of(context).primaryColorLight,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      fontFamily: 'Raleway',
                                    ),
                                )
                              ]
                            ),
                        ),
                        VerticalDivider(color: Theme.of(context).primaryColor, thickness: 1,),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'CLIENTE',
                                style:
                                  TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Raleway',
                                  ),
                                textAlign: TextAlign.left,
                              ),
                              selectedClient.id != ""
                              ? Text(
                                selectedClient.id == '' ? 'NoClient' : '${selectedClient.name!} ${selectedClient.surname!}',
                                style:
                                  TextStyle(
                                    color: Theme.of(context).primaryColorLight,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'Raleway',
                                  )
                              )
                              : InkWell(
                                onTap: () {
                                  // showAddClientPopup(context);
                                },

                                child: Row(
                                  children:[
                                    Icon(
                                      Icons.add_rounded,
                                      color: Theme.of(context).primaryColorLight,
                                      size: 12,
                                    ),
                                    Text(
                                      'Associa Cliente',
                                      style: TextStyle(
                                        color: Theme.of(context).primaryColorLight,
                                        fontSize: 12,
                                        fontFamily: 'Raleway',
                                        decoration: TextDecoration.underline,
                                        decorationColor: Theme.of(context).primaryColorLight,
                                      ),
                                    ),
                                  ]
                                )
                              )
                            ]
                          ),
                        ),
                      ],
                    )
                  )
              ),
              SizedBox(
                height: footerHeight,
                width: !isMateriotechSelected ? MediaQuery.of(context).size.width * .40 : MediaQuery.of(context).size.width * .30,
                // child: !isMateriotechSelected
                //   ? null
                //   : Row(
                //     mainAxisAlignment: MainAxisAlignment.end,
                //     crossAxisAlignment: CrossAxisAlignment.center,
                //     children: [
                //       Padding(
                //         padding: const EdgeInsets.only(right: 30),
                //         child: SizedBox(
                //           height: footerHeight*.9,
                //           width: MediaQuery.of(context).size.width * .20,
                //           child: ElevatedButton(
                //             onPressed: () async {
                //               // setState(() {
                //               //   _loading = true;
                //               // });
                //               // Map<String, dynamic> data = {
                //               //   'name': selectedMateriotech.name,
                //               //   'created': DateTime.now().millisecondsSinceEpoch,
                //               //   'city': selectedMateriotech.city,
                //               //   'clientId': selectedMateriotech.clientId,
                //               //   'newarcProjectId': selectedMateriotech.newarcProjectId,
                //               // };
                //               // NewarcMateriotechSelection _selectedMateriotech = await saveMateriotechToFirestore(NewarcMateriotechSelection(data));
                //               // await _fetchMaterioteques();
                //               // setState(() {
                //               //   selectedMateriotech = materiotechsList.where((elem) => elem.id == _selectedMateriotech.id).first;
                //               //   _loading = false;
                //               // });
                //             },
                //             style: ElevatedButton.styleFrom(
                //               backgroundColor: Theme.of(context).primaryColorLight,
                //               foregroundColor: Theme.of(context).primaryColorDark,
                //               side: BorderSide(color: Theme.of(context).primaryColorLight),
                //               shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                //             ),
                //             child:
                //               Row(
                //                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                //                 crossAxisAlignment: CrossAxisAlignment.center,
                //                 children: [
                //                   const Icon(
                //                     Icons.add_rounded
                //                   ),
                //                   Text(
                //                     'Aggiungi moodboard',
                //                     selectionColor: Theme.of(context).primaryColorDark,
                //                     style: const TextStyle(
                //                       fontSize: 15,
                //                       fontWeight: FontWeight.w600,
                //                       fontFamily: 'Raleway',
                //                     ),
                //                   )
                //                 ]
                //               ),
                //           )
                //         )
                //       )
                //     ]
                //   )
              ),
            ]
          ),
        )
      ;
    }
    else {
      return
        SizedBox(
          height: footerHeight,
        )
      ;
    }
  }

  Future<NewarcMateriotechSelection> saveMateriotechToFirestore(NewarcMateriotechSelection selection) async {
    DocumentReference savedId;
    try {
     savedId = await FirebaseFirestore.instance.collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS).add(selection.toMap());
     selection.id = savedId.id;
    } catch (e, stackTrace) {
      print('Error in saving materiotech data to firestore: $e');
      print('stackTrace: $stackTrace');
    }
    return selection;
  }

  Widget selectView(viewHeight, headerHeight, footerHeight) {
    if (selectedView == 'select_project') {
      return _buildSelectProjectView(viewHeight);
    }
    else if (selectedView == 'existing_project') {
      return _buildClientProjectView(viewHeight);
    }
    else if (selectedView == 'new_project') {
      return _buildNewProjectView(viewHeight);
    }
    else if (selectedView == 'dashboard_materioteca') {
      return _buildMateriotechDashBoard(viewHeight, headerHeight, footerHeight);
    }
    else {
      return Center(child: Text(
        'Selected view not valid',
        style: TextStyle(color: Theme.of(context).primaryColorLight),
      ));
    }
  }

  Widget _buildSelectProjectView(maxHeight){
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        // Calculate proportional heights
        double buttonSpacing = maxHeight * .02;
        double topSpacing = maxHeight * .25;
        double bottomSpacing = maxHeight *.2;
        double buttonWidth = MediaQuery.of(context).size.width * .25;
        // double buttonHeight = (maxHeight - topSpacing - bottomSpacing - buttonSpacing*2)/3;
        BorderRadius buttonBorderRadius = BorderRadius.circular(10);
        double buttonHeight = 60;
        const TextStyle buttonTextStyle = TextStyle(
                                                  fontSize: 16,
                                                  fontFamily: 'Raleway',
                                                  fontWeight: FontWeight.w500
                                                );
        return _loading
        ? Center(
                child: CircularProgressIndicator(
                color: Theme.of(context).primaryColorLight,
              ))
        : Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: topSpacing),
              SizedBox(
                height: buttonHeight,
                width: buttonWidth,
                child:
                  ElevatedButton(
                    onPressed: () => setState(() {
                      selectedView = 'new_project';
                      isMateriotechNew = true;}),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColorLight,
                      foregroundColor: Theme.of(context).primaryColorDark,
                      side: BorderSide(color: Theme.of(context).primaryColorLight),
                      shape: RoundedRectangleBorder(borderRadius: buttonBorderRadius),

                    ),
                    child:
                      Text(
                        'Nuova moodboard',
                        selectionColor: Theme.of(context).primaryColorDark,
                        style: buttonTextStyle,
                        textAlign: TextAlign.center,
                      ),
                  )
              ),
              SizedBox(height: buttonSpacing),
              SizedBox(
                height: buttonHeight,
                width: buttonWidth,
                child:
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        selectedView = 'existing_project';
                        isMateriotechNew = false;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColorDark,
                      foregroundColor: Theme.of(context).primaryColorLight,
                      side: BorderSide(color: Theme.of(context).primaryColorLight),
                      shape: RoundedRectangleBorder(borderRadius: buttonBorderRadius),
                      // minimumSize: Size(200, 50),
                    ),
                    child:
                      Text(
                        'Apri moodboard',
                        selectionColor: Theme.of(context).primaryColorDark,
                        style: buttonTextStyle,
                        textAlign: TextAlign.center,
                      ),
                  )
                ),
              SizedBox(height: bottomSpacing),
            ],
          ),
        );
      },
    );
  }

  Widget _buildClientProjectView(maxHeight){
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double topHeigthSpacing = maxHeight*.10;
        double centralHeigth = maxHeight*.80;
        double bottomHeigthSpacing = maxHeight*.10;
        double centralWidth = MediaQuery.of(context).size.width * .3;
        return _loading
        ? Center(
                child: CircularProgressIndicator(
                color: Theme.of(context).primaryColorLight,
              ))
        : Column(
          children: [
            SizedBox(height: topHeigthSpacing,),
            SizedBox(
              height: centralHeigth,
              width: centralWidth,
              child:
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child:
                        Text(
                          'Apri Moodboard',
                          style: TextStyle(
                            fontFamily: 'Raleway',
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).primaryColorLight,),
                        )
                    ),
                    const SizedBox(height: 25,),
                    SizedBox(
                      height: centralHeigth - bottomHeigthSpacing - topHeigthSpacing,
                      width: centralWidth,
                      child: SingleChildScrollView(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children:[
                              const SizedBox(height: 25,),
                              Text(
                                'Cerca cliente o indirizzo',
                                style: TextStyle(
                                  fontFamily: 'Raleway',
                                  fontSize: 16,
                                  color: Theme.of(context).primaryColorLight,),
                              ),
                              const SizedBox(height: 10,),
                              SearchSelectBox(
                                controller: filterMateriotech,
                                options:
                                  materiotechsList
                                  .where((mate) => mate.clientId != '')
                                  .fold<Map<String, NewarcMateriotechSelection>>({}, (map, item) {
                                    final currentItem = map[item.name];
                                    if (currentItem == null) {
                                      map[item.name!] = item;
                                    }
                                    else if (currentItem.insertTimestamp! > item.insertTimestamp!){
                                      map[item.name!] = item;
                                    }
                                    return map;
                                  })
                                  .values
                                  .map((riotech) {
                                    String name = "Deleted Client - ${riotech.name!}";
                                    if (clientsList.where((cli) => cli.id == riotech.clientId).isNotEmpty){
                                      name = clientsList.where((cli) => cli.id == riotech.clientId,).first.name! + ' ' +
                                        clientsList.where((cli) => cli.id == riotech.clientId,).first.surname! + ' - '+
                                        riotech.name!;
                                    }
                                    return {
                                      'id': riotech.id!,
                                      'name': name,
                                    };
                                  }).toList(),
                                onSelection: (selected) {
                                  setState(() {
                                    selectedMateriotech =
                                      materiotechsList.where((elem) => elem.id == selected,).first;
                                    selectedClient =
                                      clientsList.where((cli) => cli.id == materiotechsList.where((mate)=> mate.id == selected).first.clientId).first;
                                  });
                                },
                              ),
                              const SizedBox(height: 25,),
                            ]),
                        ),
                    )
                  ]
                ),
            ),
            SizedBox(height: bottomHeigthSpacing,),
          ],
        );
      }
    );
  }

  Widget _buildNewProjectView(maxHeight){
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double topHeigthSpacing = maxHeight*.10;
        double centralHeigth = maxHeight*.80;
        double bottomHeigthSpacing = maxHeight*.10;
        double columnWidth = MediaQuery.of(context).size.width * .3;
        double buttonWidth = MediaQuery.of(context).size.width * .25;
        double buttonHeight = 60;
        BorderRadius buttonBorderRadius = BorderRadius.circular(10);
        const TextStyle buttonTextStyle = TextStyle(
          fontSize: 16,
          fontFamily: 'Raleway',
          fontWeight: FontWeight.w500
        );
        return _loading
        ? Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).primaryColorLight,
          )
        )
        : Column(
          children: [
            SizedBox(height: topHeigthSpacing),
            SizedBox(
              width: columnWidth,
              height: centralHeigth,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child:
                      Text(
                        'Crea Moodboard',
                        style: TextStyle(
                          fontFamily: 'Raleway',
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).primaryColorLight,),
                      )
                  ),
                  const SizedBox(height: 25,),
                  SizedBox(
                    height: centralHeigth - bottomHeigthSpacing - topHeigthSpacing,
                    width: columnWidth,
                    child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children:[
                            const SizedBox(height: 25,),
                            Text(
                              'Cerca cliente',
                              style: TextStyle(
                                fontFamily: 'Raleway',
                                fontSize: 16,
                                color: Theme.of(context).primaryColorLight,),
                            ),
                            const SizedBox(height: 10,),
                            SearchSelectBox(
                              controller: filterClient,
                              options: clientsList
                                // select clients not having a materiotechselection & map to list
                                .where((cli) => materiotechsList.where((mate) => mate.clientId == cli.id).isEmpty)
                                .map((cli){return {'id': cli.id!, 'name': '${cli.name!} ${cli.surname!}'};}).toList(),
                              onSelection: (selectedId) {
                                setState(() {
                                  selectedClient = clientsList.where((cli) => cli.id == selectedId).first;
                                });
                              },
                            ),
                            const SizedBox(height: 25,),
                            Center(
                              child: SizedBox(
                                height: buttonHeight,
                                width: buttonWidth,
                                child:
                                  ElevatedButton(
                                    onPressed: () {
                                      showAddContactPopup();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Theme.of(context).primaryColorLight,
                                      foregroundColor: Theme.of(context).primaryColorDark,
                                      side: BorderSide(color: Theme.of(context).primaryColorLight),
                                      shape: RoundedRectangleBorder(borderRadius: buttonBorderRadius),

                                    ),
                                    child:
                                      Text(
                                        'Nuovo Cliente',
                                        selectionColor: Theme.of(context).primaryColorDark,
                                        style: buttonTextStyle,
                                        textAlign: TextAlign.center,
                                      ),
                                  )
                              ),
                            ),
                        ]
                        ),
                      ),
                  )
                ]
              )
            ),
            SizedBox(height: bottomHeigthSpacing,)
          ],
        );
      }
    );
  }

  Future<void> showAddContactPopup() async {
    contactNameController.clear();
    contactSurnameController.clear();
    contactEmailController.clear();
    contactPhoneController.clear();
    contactAddressInfo = BaseAddressInfo.empty();
    renovatorController.clear();
    formProgressMessage = '';
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext _context) {
        return StatefulBuilder(builder: (__context, setState) {
          return Center(
              child: BaseNewarcPopup(
                  title: "Aggiungi contatto",
                  buttonText: "Aggiungi contatto",
                  buttonColor: Theme.of(context).primaryColorLight,
                  buttonTextColor: Theme.of(context).primaryColorDark,
                  isShowCloseIcon: true,
                  formErrorMessage: [formProgressMessage],
                  onPressed: () async {
                    setState(() {
                      formProgressMessage = 'Salvando';
                    });

                    try {
                      Map<String, dynamic> contactData = {
                        'name': contactNameController.text,
                        'surname': contactSurnameController.text,
                        'email': contactEmailController.text,
                        'phone': contactPhoneController.text,
                      };

                      if (!contactAddressInfo.isValidAddress()) {
                        setState(() {
                          formProgressMessage = 'Indirizzo non valido';
                        });
                        return false;
                      }
                      RenovationContact renovationContact = RenovationContact.empty();
                      renovationContact.name = contactNameController.text;
                      renovationContact.surname = contactSurnameController.text;
                      renovationContact.email = contactEmailController.text;
                      renovationContact.phone = contactPhoneController.text;
                      renovationContact.created = DateTime.now().millisecondsSinceEpoch;
                      renovationContact.streetAddress = contactAddressInfo.toShortAddress();
                      renovationContact.city = contactAddressInfo.city;
                      renovationContact.personInfo = BasePersonInfo.fromMap(contactData);
                      // renovationContact.addressInfo = contactAddressInfo;
                      renovationContact.assignedRenovatorId = renovatorController.text;

                      DocumentReference<Map<String, dynamic>> renoContact =
                      await FirebaseFirestore.instance
                          .collection(
                          appConfig.COLLECT_RENOVATION_CONTACTS)
                          .add(renovationContact.toMap());
                      await _fetchClients();
                    } catch (e, s) {
                      setState(() {
                        formProgressMessage = 'Errore';
                      });
                      print({e, s});
                      return false;
                    }
                  },
                  // To make custom popup using the bluprint of  RenovationContactPopup
                  column: Container(
                    width: 400,
                    height: 400,
                    child: ListView(
                      children: [
                        AddressSearchBar(
                          label: "Indirizzo Ristrutturazione",
                          labelColor: Theme.of(context).primaryColorLight,
                          inputTextColor: Theme.of(context).primaryColorLight,
                          backgroundColor: Theme.of(context).primaryColorDark,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Inserisci un indirizzo valido';
                            }
                          },
                          onPlaceSelected: (selectedPlace){
                            debugPrint('Selected place: \n$selectedPlace');
                            BaseAddressInfo selectedAddress = BaseAddressInfo.fromMap(selectedPlace['place']);
                            if (selectedAddress.isValidAddress()){
                              contactAddressInfo = selectedAddress;
                            } else {
                              contactAddressInfo = BaseAddressInfo.empty();
                            }
                          }
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Nome",
                              labelColor: Theme.of(context).primaryColorLight,
                              inputTextColor: Theme.of(context).primaryColorLight,
                              validator: (value) {
                                if (value == '') {
                                  return 'Obbligatorio';
                                }
                                return null;
                              },
                              controller: contactNameController,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Cognome",
                              labelColor: Theme.of(context).primaryColorLight,
                              inputTextColor: Theme.of(context).primaryColorLight,
                              controller: contactSurnameController,
                              validator: (value) {
                                if (value == '') {
                                  return 'Obbligatorio';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "E-Mail",
                              labelColor: Theme.of(context).primaryColorLight,
                              inputTextColor: Theme.of(context).primaryColorLight,
                              controller: contactEmailController,
                              validator: (value) {
                                final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
                                if (!emailRegex.hasMatch(value)) {
                                  return 'Inserisci un indirizzo email valido';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            CustomTextFormField(
                              label: "Telefono",
                              labelColor: Theme.of(context).primaryColorLight,
                              inputTextColor: Theme.of(context).primaryColorLight,
                              inputFormatters: [phoneNumberMaskFormatterIt],
                              controller: contactPhoneController,
                              validator: (value) {
                                if (value == '') {
                                  return 'Obbligatorio';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        loadingRenovators
                            ? CircularProgressIndicator(
                          color: Theme.of(context).primaryColor,
                        )
                            : Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  NarFormLabelWidget(
                                    label: "Assegnazione",
                                    textColor: Theme.of(context).primaryColorLight,
                                    fontSize: 13,
                                    fontWeight: '500',
                                  ),
                                  SizedBox(height: 4),
                                  NarImageSelectBoxWidget(
                                    options: renovatorsList
                                        .where((e) =>
                                            e.isActive == true &&
                                            e.isArchived == false)
                                        .map((e) {
                                      return {
                                        'value': e.id,
                                        'label':
                                            e.firstName! + " " + e.lastName!
                                      };
                                    }).toList(),
                                    controller:
                                        renovatorController,
                                    validationType: 'required',
                                    parametersValidate: 'Obbligatorio',
                                    onChanged: (dynamic val) {
                                      renovatorsList
                                          .where((element) =>
                                              element.id ==
                                              renovatorController.text)
                                          .first;
                                setState(() {});
                              },
                            ),
                          ],
                        ),
                        // SizedBox(height: 10),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.center,
                        //   children: [
                        //     NarFormLabelWidget(
                        //         label: formProgressMessage,
                        //         textColor: Colors.red,)
                        //   ],
                        // )
                      ],
                    ),
                  )));
        });
      });
  }

  void showMoodboardPopup(BuildContext context) {
    showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          final verticalPopupPadding = MediaQuery.of(context).size.height*.05;
          final horizontalPopupPadding = MediaQuery.of(context).size.width*.05;

          return Center(
            child: Stack(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: verticalPopupPadding,
                    horizontal: horizontalPopupPadding,
                  ),
                  color: Theme.of(context).primaryColorLight,
                  // child: waitingForGenerationView(),
                  child: 
                  errorGenerationView(),
                  
                ),
                Positioned(
                  top: verticalPopupPadding,
                  right: horizontalPopupPadding,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: (){
                        Navigator.pop(context);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColorDark,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: SvgPicture.asset(
                            'assets/icons/close_popup.svg',
                            height: 15,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).primaryColorLight,
                              BlendMode.srcIn
                            )
                          )
                        )
                      )
                    ),
                  )
                )
              ],
            )
          );
        });
      }
    );
  }

  Widget waitingForGenerationView(){
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomProgressIndicator(
              assetPath: 'assets/icons/smart-ai.png', 
              color: Theme.of(context).primaryColorDark,
              animationType: 'disappearing',
              size: 200,
            ),
            Text(
              "Composizione moodboard in corso...",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                fontFamily: 'Raleway',
                color: Theme.of(context).primaryColorDark,
              ),
            )
          ]
        ),
      ],
    );
  }

  Widget errorGenerationView(){
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_outlined,
              size: 200,
              color: Colors.red,),
            Text(
              "Errore nella generazione della moodboard",
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                fontFamily: 'Raleway',
                color: Theme.of(context).primaryColorDark,
              ),
            )
          ]
        ),
      ],
    );
  }

  void showErrorPopup(BuildContext context, String title, String message) {
    showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return Center(
              child: BaseNewarcPopup(
                  title: title,
                  noButton: true,
                  column: Container(
                    width: 400,
                    child: Column(
                      children: [
                        Icon(Icons.error_outline, size: 40, color: Theme.of(context).primaryColorLight),
                        SizedBox(height: 10,),
                        Text(
                          message,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w400,
                            fontFamily: 'Raleway',
                            color: Theme.of(context).primaryColorLight,
                          ),
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.clip,
                        ),
                      ],
                    ),
                  ),
              )
          );
        });
      }
    );
  }

  _buildMateriotechDashBoard(maxHeight, headerHeight, footerHeight){
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double elementsSpacer = maxHeight*.05;
        double thirdSpacer = (MediaQuery.of(context).size.width -3*elementsSpacer)/3;
        double moodboardAndApplicaButtonWidth = 150;
        return _loading ?
          Center(child: CircularProgressIndicator(
            color: Theme.of(context).primaryColorLight,
          ))
          : Padding(
            padding: EdgeInsets.all(elementsSpacer),
            child: Column(
              children: [
                Container(
                  width: thirdSpacer*3 + elementsSpacer,
                  height: headerHeight*2,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Color(0xff9f9f9f),
                      width: 1
                    ),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(elementsSpacer/2),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Aggiungi ambienti',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            fontFamily: 'Raleway',
                            color: Theme.of(context).primaryColorLight,
                          ),
                        ),
                        SizedBox(
                          height: footerHeight,
                          child: ElevatedButton(
                            onPressed: (){
                              showAddRoomPopup();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).primaryColorLight,
                              foregroundColor: Theme.of(context).primaryColorDark,
                              disabledBackgroundColor: Theme.of(context).primaryColor,
                              disabledForegroundColor: Theme.of(context).primaryColorDark,
                              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                            ),
                            child: Text(
                              'Aggiungi ambiente',
                              selectionColor: Theme.of(context).primaryColorDark,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                fontFamily: 'Raleway',
                              ),
                            ),
                          )
                        )
                      ],
                    )
                  ),
                ),
                SizedBox(height: elementsSpacer/2,),
                Row(
                  children: [
                    Container(
                      width: thirdSpacer,
                      height: maxHeight - headerHeight*2 - footerHeight - elementsSpacer/1.9,
                      decoration: BoxDecoration(
                        color: Color(0xff1d1d1d),
                        // border: Border.all(
                        //   color: Color(0xff9f9f9f),
                        //   width: 1
                        // ),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child:
                        Padding(
                          padding: EdgeInsets.all(elementsSpacer/2),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                'Aggiungi materiali',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  fontFamily: 'Raleway',
                                  color: selectedRoom.name==null ? Theme.of(context).primaryColor : Theme.of(context).primaryColorLight,
                                ),
                              ),
                              const SizedBox(height: 20,),
                              // SizedBox(
                              //   height: 100,
                              //   width: thirdSpacer-elementsSpacer,
                              //   child: ElevatedButton(
                              //     style: ButtonStyle(
                              //       shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                              //         borderRadius: BorderRadius.circular(10),
                              //       )),
                              //       textStyle: const WidgetStatePropertyAll(TextStyle(
                              //         fontSize: 12,
                              //         fontWeight: FontWeight.w400,
                              //         fontFamily: 'Raleway',
                              //       )),
                              //       backgroundColor: WidgetStatePropertyAll(Color(0xff1f1f1f)),
                              //       foregroundColor: WidgetStatePropertyAll(Theme.of(context).primaryColor)
                              //     ),
                              //     onPressed: (){},
                              //     child: const Column(
                              //       mainAxisAlignment: MainAxisAlignment.center,
                              //       crossAxisAlignment: CrossAxisAlignment.center,
                              //       children: [
                              //         Icon(
                              //           Icons.add,
                              //           size: 40,
                              //         ),
                              //         SizedBox(height: 2,),
                              //         Text('Avvicina il dispositivo a un materiale per aggiungerlo', textAlign: TextAlign.center,)
                              //       ],
                              //     ),
                              //   )
                              // ),
                              // const SizedBox(height: 10,),
                              Text(
                                'Categoria',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Raleway',
                                  color: selectedRoom.name==null ? Theme.of(context).primaryColor : Theme.of(context).primaryColorLight,
                                ),
                              ),
                              const SizedBox(height: 5,),
                              NarSelectBoxWidget(
                                options: selectedRoom.name==null ? [] : (materialsList.map((material) => material.materialType!.toCapitalized()).toSet().toList()..sort((a, b) => a.compareTo(b),)),
                                controller: filterCategoria,
                                contentPadding: EdgeInsets.only(top: 10, bottom: 10, right: 2, left: 10),
                                menuMaxHeight: 200,
                                onChanged: (value) async {
                                  manufacturersList = [];
                                  collectionsList = [];
                                  variantsList = [];
                                  await _fetchNewarcMaterialManufacturers(value.toLowerCase());
                                  setState(() {
                                    selectedCategoria = filterCategoria.text.toLowerCase();
                                    selectedProduttore = null;
                                    filterProduttore.clear();
                                    selectedCollezione = null;
                                    filterCollezione.clear();
                                    selectedProdotto = null;
                                    filterProdotto.clear();
                                  });
                                },
                              ),
                              const SizedBox(height: 10,),
                              Text(
                                'Produttore',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Raleway',
                                  color: selectedCategoria != "" ? Theme.of(context).primaryColorLight : Theme.of(context).primaryColor,
                                ),
                              ),
                              const SizedBox(height: 5,),
                              NarSelectBoxWidget(
                                options: manufacturersList.map((manufacturer) => manufacturer.name!).toSet().toList()..sort((a, b) => a.compareTo(b),),
                                controller: filterProduttore,
                                contentPadding: EdgeInsets.only(top: 10, bottom: 10, right: 2, left: 10),
                                menuMaxHeight: 200,
                                onChanged: (value) async {
                                  NewarcMaterialManufacturer manufacturer = manufacturersList.where((manufacturer) => manufacturer.name == value).first;
                                  collectionsList = [];
                                  variantsList = [];
                                  await _fetchNewarcMaterialManufacturerCollections(manufacturer);
                                  setState(() {
                                    selectedProduttore = manufacturer;
                                    selectedCollezione = null;
                                    filterCollezione.clear();
                                    selectedProdotto = null;
                                    filterProdotto.clear();
                                  });
                                },
                              ),
                              const SizedBox(height: 10,),
                              Text(
                                'Collezione',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Raleway',
                                  color: (selectedProduttore != null && selectedCategoria != "") ? Theme.of(context).primaryColorLight : Theme.of(context).primaryColor,
                                ),
                              ),
                              const SizedBox(height: 5,),
                              NarSelectBoxWidget(
                                options: collectionsList.map((collection) => collection.name!).toSet().toList()..sort((a, b) => a.compareTo(b),),
                                controller: filterCollezione,
                                contentPadding: EdgeInsets.only(top: 10, bottom: 10, right: 2, left: 10),
                                menuMaxHeight: 200,
                                onChanged: (value) async {
                                  NewarcMaterialManufacturerCollection collection = collectionsList.where((collection) => collection.name == value).first;
                                  variantsList = [];
                                  await _fetchNewarcMaterialVariants(collection);
                                  setState(() {
                                    selectedCollezione = collection;
                                    selectedProdotto = null;
                                    filterProdotto.clear();
                                  });
                                },
                              ),
                              const SizedBox(height: 10,),
                              Text(
                                'Prodotto',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Raleway',
                                  color: (selectedCollezione != null && selectedProduttore != null && selectedCategoria != "") ? Theme.of(context).primaryColorLight : Theme.of(context).primaryColor,
                                ),
                              ),
                              const SizedBox(height: 5,),
                              NarSelectBoxWidget(
                                options: variantsList.map((value) => value.print()).toList()..sort((a, b) => a.compareTo(b),),
                                controller: filterProdotto,
                                contentPadding: EdgeInsets.only(top: 10, bottom: 10, right: 2, left: 10),
                                menuMaxHeight: 200,
                                onChanged: (value) {
                                  NewarcMaterialVariantExtension variant = variantsList.where((variant) => variant.print() == value).first;
                                  setState(() {
                                    selectedProdotto = variant;
                                  });
                                },
                              ),
                              Expanded(child: SizedBox()),
                              SizedBox(
                                height: 50,
                                width: thirdSpacer - elementsSpacer,
                                child: ElevatedButton(
                                  onPressed: (selectedProdotto == null || selectedCollezione == null || selectedProduttore == null || selectedCategoria == "" || selectedRoom.name==null) ? null
                                   : () async {
                                    selectedMateriotech.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                    selectedRoom.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                    selectedMoodboard.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                    
                                    selectedMoodboard.materialVariantIds.add(selectedProdotto!.firebaseId);
                                    selectedMoodboard.smartMoodboard.materialIds.add(selectedProdotto!.naMaterialId!);
                                    // selectedRoom.moodboards.where((item) => item.code == selectedMoodboard.code).first.materialVariantIds.add(selectedProdotto!.firebaseId);
                                    // selectedMateriotech.rooms.where((room) => room.name == selectedRoom.name).first.moodboards.where((item) => item.code == selectedMoodboard.code).first.materialVariantIds.add(selectedProdotto!.firebaseId);
                                    await FirebaseFirestore.instance
                                      .collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS)
                                      .doc(selectedMateriotech.id!)
                                      .update(selectedMateriotech.toMap());
                                    setState(() {});
                                   },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Theme.of(context).primaryColorLight,
                                    foregroundColor: Theme.of(context).primaryColorDark,
                                    disabledBackgroundColor: Theme.of(context).primaryColor,
                                    disabledForegroundColor: Theme.of(context).primaryColorDark,
                                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                                  ),
                                  child:
                                    Text(
                                      'Seleziona materiale',
                                      selectionColor: Theme.of(context).primaryColorDark,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: 'Raleway',
                                      ),
                                    ),
                                )
                              ),
                            ],
                          )
                        ),
                    ),
                    SizedBox(width: elementsSpacer,),
                    Container(
                      width: thirdSpacer*2,
                      height: maxHeight - headerHeight*2 - footerHeight - elementsSpacer/1.9,
                      decoration: BoxDecoration(
                        color: Color(0xff1d1d1d),
                        borderRadius: BorderRadius.circular(10),
                        // border: Border.all(
                        //   color: Color(0xff9f9f9f),
                        //   width: 1,
                        // ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(elementsSpacer/2),
                        child: selectedRoom.name == null
                        ? Center(
                          child: Text(
                            'Materioteca vuota! Definisci un ambiente e inserisci i materiali per vederli qui',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Raleway',
                              color: Theme.of(context).primaryColor
                            ),
                          )
                        )
                        :
                        Stack(
                          fit: StackFit.loose,
                          children: [
                            Positioned(
                              child: Text(
                                selectedRoom.name!,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w700,
                                  fontFamily: 'Raleway',
                                  color: Theme.of(context).primaryColorLight
                                ),
                              )
                            ),
                            selectedMoodboard.materialVariantIds.isEmpty
                            ? Center(
                              child: Text(
                                "Moodboard vuota! Inserisci i materiali nell'ambiente per vederli qui",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'Raleway',
                                  color: Theme.of(context).primaryColor
                                ),
                              )
                            )
                            : Positioned(
                              top: 45, // Leave space for the room name at top
                              left: 0,
                              right: 0,
                              bottom: 60, // Leave space for the bottom controls
                              child: GridView.builder(
                                padding: EdgeInsets.only(bottom: 10),
                                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 4,
                                  childAspectRatio: 1,
                                  crossAxisSpacing: 10,
                                  mainAxisSpacing: 10,
                                ),
                                itemCount: selectedMoodboard.materialVariantIds.length,
                                itemBuilder: (context, index) {
                                  return FutureBuilder<NewarcMaterialVariantExtension>(
                                    future: fetchMaterialVariantWithRelatedData(selectedMoodboard.materialVariantIds[index]),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState == ConnectionState.waiting) {
                                        return Center(child: CircularProgressIndicator(color: Theme.of(context).primaryColorLight,));
                                      } else if (snapshot.hasError) {
                                        return Center(child: Text('Error: ${snapshot.error}', style: TextStyle(color: Colors.red)));
                                      } else if (!snapshot.hasData) {
                                        return Center(child: Text('No data found', style: TextStyle(color: Colors.red)));
                                      }
                                      return Align(
                                        alignment: Alignment.centerLeft,
                                        child: NewarcMaterialIcon(
                                          material: snapshot.data,
                                          onCloseTap: (materialId) async {
                                            setState(() {
                                              _loading = true;
                                            });
                                            selectedMateriotech.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            selectedRoom.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            selectedMoodboard.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            
                                            selectedMoodboard.materialVariantIds
                                              .removeAt(selectedMoodboard.materialVariantIds
                                                .indexWhere((item) => item == materialId));
                                            selectedMoodboard.smartMoodboard.materialIds
                                              .removeAt(selectedMoodboard.smartMoodboard.materialIds
                                                .indexWhere((item) => item == snapshot.data!.naMaterialId));

                                            await FirebaseFirestore.instance
                                              .collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS)
                                              .doc(selectedMateriotech.id!)
                                              .update(selectedMateriotech.toMap());
                                            setState(() {
                                              _loading = false;
                                            });
                                          }
                                        ),
                                      );
                                    }
                                  );
                                }
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              left: 0,
                              child: Container(
                                padding: EdgeInsets.only(right: 10, left: 10),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(),
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 2),
                                          child: Text(
                                            'I TUOI AMBIENTI',
                                            style:
                                              TextStyle(
                                                color: Theme.of(context).primaryColor,
                                                fontSize: 10,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Raleway',
                                              ),
                                            textAlign: TextAlign.left,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 170,
                                          height: 25,
                                          child:
                                          DropdownButtonFormField(
                                            icon: SvgPicture.asset(
                                              'assets/icons/arrow_down.svg',
                                              width: 5,
                                              height: 5,
                                              color: Theme.of(context).primaryColorLight,
                                            ),
                                            iconSize: 10,
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              fontFamily: 'Raleway'
                                            ),
                                            decoration: const InputDecoration(
                                              isDense: true,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              border: InputBorder.none,
                                            ),
                                            dropdownColor: Color(0xff1f1f1f),
                                            value: selectedRoom,
                                            itemHeight: null,
                                            items: selectedMateriotech.rooms
                                              .map((thing) =>
                                                DropdownMenuItem(
                                                  value: thing,
                                                  child: Text(thing.name!, overflow: TextOverflow.ellipsis,),
                                                )
                                              ).toList(),
                                            onChanged: (value) {
                                              if (value != null) {
                                                if (value.name! != selectedRoom.name) {
                                                  setState(() {
                                                    // _loading = true;
                                                    selectedRoom = value;
                                                    selectedMoodboard = value.moodboards.isNotEmpty ? value.moodboards.first : NewarcMateriotechMoodboard.empty();
                                                    // _loading = false;
                                                  });
                                                }
                                              }
                                            },
                                          )
                                        )
                                      ]
                                    ),
                                    SizedBox(width: 20,),
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(),
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 2),
                                          child: Text(
                                            'MOODBOARD',
                                            style:
                                              TextStyle(
                                                color: Theme.of(context).primaryColor,
                                                fontSize: 10,
                                                fontWeight: FontWeight.w500,
                                                fontFamily: 'Raleway',
                                              ),
                                            textAlign: TextAlign.left,
                                          ),
                                        ),
                                        SizedBox(
                                          width: 120,
                                          height: 25,
                                          child:
                                          DropdownButtonFormField(
                                            icon: SvgPicture.asset(
                                              'assets/icons/arrow_down.svg',
                                              width: 5,
                                              height: 5,
                                              color: Theme.of(context).primaryColorLight,
                                            ),
                                            iconSize: 10,
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              fontFamily: 'Raleway'
                                            ),
                                            decoration: const InputDecoration(
                                              isDense: true,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              border: InputBorder.none,
                                            ),
                                            dropdownColor: Color(0xff1f1f1f),
                                            value: selectedMoodboard,
                                            itemHeight: null,
                                            items: selectedRoom.moodboards
                                              .map((thing) =>
                                                DropdownMenuItem(value: thing,
                                                                  child: Text('Moodboard ${thing.code ?? "#"}', overflow: TextOverflow.ellipsis,)
                                                )
                                              ).toList() + [
                                                DropdownMenuItem(value: NewarcMateriotechMoodboard.empty(),
                                                                  child: Text(
                                                                    'Nuova',
                                                                    style: const TextStyle(
                                                                      color: Colors.white,
                                                                      fontSize: 12,
                                                                      fontWeight: FontWeight.w400,
                                                                      fontFamily: 'Raleway'
                                                                    ),
                                                                  )
                                                )
                                              ],
                                            onChanged: (value) async {
                                              if (value != null) {
                                                if (value.code == null) {
                                                  value.code = selectedRoom.moodboards.length + 1;
                                                  selectedRoom.moodboards.add(value);
                                                  selectedRoom.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                                  selectedMateriotech.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                                  await FirebaseFirestore.instance
                                                    .collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS)
                                                    .doc(selectedMateriotech.id!)
                                                    .update(selectedMateriotech.toMap());
                                                  setState(() {
                                                    selectedMoodboard = value;
                                                  });
                                                } else if (value.code! != selectedMoodboard.code) {
                                                  setState(() {
                                                    selectedMoodboard = value;
                                                  });
                                                }
                                              }
                                            },
                                          )
                                        )
                                      ]
                                    ),
                                  ],
                                )
                              )
                            ),
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: MouseRegion(
                                cursor: SystemMouseCursors.click,
                                child: GestureDetector(
                                  child: Container(
                                    width: moodboardAndApplicaButtonWidth,
                                    decoration: BoxDecoration(
                                      color: Color(0xff303030),
                                      borderRadius: BorderRadius.circular(10)
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                                        children: [
                                          Image.asset(
                                            'assets/icons/moodboard.png',
                                            width: 25,
                                            color: Theme.of(context).primaryColorLight,
                                          ),
                                          // SizedBox(width: 10,),
                                          Text(
                                            "Moodboard",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w600,
                                              fontFamily: 'Raleway',
                                              color: Theme.of(context).primaryColorLight
                                            ),
                                          ),
                                          // TODO uncomment if AI logo needed when creating moodboard.
                                          // SizedBox(width: 5,),
                                          // Column(
                                          //   children: [
                                          //     Image.asset(
                                          //       'assets/icons/smart-ai.png',
                                          //       width: 18,
                                          //       alignment: Alignment.topCenter,
                                          //       color: Theme.of(context).primaryColorLight,
                                          //     ),
                                          //     SizedBox(height: 10,),
                                          //   ],
                                          // ),
                                        ],
                                      ),
                                    )
                                  ),
                                  onTap: () async {
                                    int minMaterialNumber = 2;
                                    // check if moodboard has at least 2 different materials
                                    if(selectedMoodboard.materialVariantIds.length < minMaterialNumber){
                                      showErrorPopup(context, "Errore", "La moodboard deve contenere almeno $minMaterialNumber materiali differenti per poter essere generata.");
                                      return;
                                    }
                                    // check if moodboard has been generated for this moodboard and material selections, if yes proceed
                                    List<String> moodboardMaterialIds = 
                                      variantsList
                                        .where((variant) => selectedMoodboard.materialVariantIds.contains(variant.firebaseId))
                                        .map((item) => item.naMaterialId!)
                                        .toList();
                                    List existingMaterialIds = selectedMoodboard.smartMoodboard.materialIds;
                                    if (existingMaterialIds.toSet().difference(moodboardMaterialIds.toSet()).isEmpty){
                                      showMoodboardPopup(context);
                                      return;
                                    } 
                                    // check if moodboard has already been generated elsewhere if yes assign it to the current moodboard and proceed
                                    bool isGenerated = false;
                                    for (NewarcMateriotechSelection mate in materiotechsList) {
                                      for (NewarcMateriotechRoom room in mate.rooms) {
                                        for (NewarcMateriotechMoodboard moodboard in room.moodboards) {
                                          if ((moodboard.smartMoodboard.materialIds).toSet().difference(moodboardMaterialIds.toSet()).isEmpty) {
                                            isGenerated = true;
                                            selectedMoodboard.smartMoodboard = moodboard.smartMoodboard.copy();
                                            selectedMoodboard.smartMoodboard.insertTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            selectedMoodboard.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            selectedRoom.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            selectedMateriotech.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                                            await FirebaseFirestore.instance
                                              .collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS)
                                              .doc(selectedMateriotech.id!)
                                              .update(selectedMateriotech.toMap());
                                            break;
                                          }
                                        }
                                        if(isGenerated) break;
                                      }
                                      if(isGenerated) break;
                                    }
                                    // if moodboard has not been generated yet, call the endpoint
                                    if(!isGenerated){
                                      Map result = await callSmartMoodboardEndpoint(
                                        materiotechId: selectedMateriotech.id!, 
                                        roomName: selectedRoom.name!, 
                                        moodboardCode: selectedMoodboard.code.toString());
                                      if (result['status'] != 200){
                                        showErrorPopup(context, "Errore interno", "Errore nella comunicazione con il server. Riprova più tardi.");
                                        return;
                                      } else {
                                        showMoodboardPopup(context);
                                      }
                                    } else {
                                      showMoodboardPopup(context);
                                    }
                                  },
                                ),
                              ),
                            ),
                            // TODO uncomment when "Applica" usage will be defined.
                            // Positioned(
                            //   bottom: 0,
                            //   right: moodboardAndApplicaButtonWidth + 10,
                            //   child: Container(
                            //     width: moodboardAndApplicaButtonWidth,
                            //     decoration: BoxDecoration(
                            //       color: Color(0xff303030),
                            //       borderRadius: BorderRadius.circular(10)
                            //     ),
                            //     child: Padding(
                            //       padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                            //       child: Row(
                            //         mainAxisAlignment: MainAxisAlignment.spaceAround,
                            //         children: [
                            //           Image.asset(
                            //             'assets/icons/moodboard.png',
                            //             width: 25,
                            //             color: Theme.of(context).primaryColorLight,
                            //           ),
                            //           // SizedBox(width: 10,),
                            //           Text(
                            //             "Applica",
                            //             textAlign: TextAlign.center,
                            //             style: TextStyle(
                            //               fontSize: 14,
                            //               fontWeight: FontWeight.w600,
                            //               fontFamily: 'Raleway',
                            //               color: Theme.of(context).primaryColorLight
                            //             ),
                            //           ),
                            //         ],
                            //       ),
                            //     )
                            //   ),
                            // ),
                          ],
                        )
                      ),
                    )
                  ],
                ),
              ],
            )
          );
      }
    );
  }

  Future<void> showAddRoomPopup() async {
    roomNameController.clear();
    roomConfigController.clear();
    formProgressMessage = '';
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext _context) {
        return StatefulBuilder(builder: (__context, _setState) {
          return Center(
              child: BaseNewarcPopup(
                  title: "Aggiungi un ambiente",
                  buttonText: "Seleziona",
                  buttonColor: Theme.of(context).primaryColorLight,
                  buttonTextColor: Theme.of(context).primaryColorDark,
                  isShowCloseIcon: true,
                  formErrorMessage: [formProgressMessage],
                  onPressed: () async {
                    _setState(() {
                      formProgressMessage = 'Salvando...';
                    });
                    try {
                      NewarcMateriotechRoom room = NewarcMateriotechRoom.empty();
                      NewarcMateriotechMoodboard moodboard = NewarcMateriotechMoodboard.empty();
                      moodboard.code = 1;
                      room.name = roomNameController.text;
                      room.configuration = roomConfigController.text != "" ? roomConfigController.text : null;
                      roomNameController.clear();
                      roomConfigController.clear();
                      room.moodboards.add(moodboard);
                      selectedMateriotech.modificationTimestamp = Timestamp.now().millisecondsSinceEpoch;
                      selectedMateriotech.rooms.add(room);
                      await FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARC_MATERIOTECH_SELECTIONS)
                          .doc(selectedMateriotech.id!)
                          .update(selectedMateriotech.toMap());
                      setState((){
                        selectedRoom = room;
                        selectedMoodboard = moodboard;
                      });
                      return true;
                    } catch (e, s) {
                      _setState(() {
                        formProgressMessage = 'Errore';
                      });
                      print({e, s});
                      return false;
                    }
                  },
                  column: Container(
                    width: 400,
                    height: 400,
                    child: ListView(
                      children: [
                        SizedBox(height: 30,),
                        Text(
                          'Scegli un ambiente',
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'Raleway',
                            color: Theme.of(context).primaryColorLight,
                          ),
                        ),
                        SizedBox(height: 10,),
                        NarSelectBoxWidget(
                          options: appConst.roomsList.where((item) => selectedMateriotech.rooms.where((room) => room.name == item).isEmpty).toList()..sort((a, b) => a.compareTo(b)),
                          controller: roomNameController,
                          validationType: 'required',
                          parametersValidate: 'Obbligatorio',
                          menuMaxHeight: 200,
                        ),
                        SizedBox(height: 20,),
                        // TODO uncomment when configurations are available
                        // Text(
                        //   'Scegli una configurazione',
                        //   style: TextStyle(
                        //     fontSize: 15,
                        //     fontWeight: FontWeight.w500,
                        //     fontFamily: 'Raleway',
                        //     color: Theme.of(context).primaryColorLight,
                        //   ),
                        // ),
                        // SizedBox(height: 10,),
                        // NarSelectBoxWidget(
                        //   options: [],
                        //   controller: roomConfigController,
                        //   validationType: 'required',
                        //   parametersValidate: 'Obbligatorio',
                        //   menuMaxHeight: 200,
                        // ),
                        SizedBox(height: 20,),

                      ],
                    ),
                  )));
        });
      });
  }
}