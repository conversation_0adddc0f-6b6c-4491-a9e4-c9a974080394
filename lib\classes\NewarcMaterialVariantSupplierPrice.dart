import 'NewarcMaterialVariant.dart';

class  NewarcMaterialVariantSupplierPrice {
String? firebaseId;
String? newarcMaterialSupplierID;
String? newarcMaterialVariantID;
String? naMaterialId;
int? insertTimestamp;
int? modificationTimestamp;
String? creationUid; // the Firebase Id of the logged in user
String? modificationUid; // the Firebase Id of the logged in user
String? code; // Code at the supplier
double? basePrice; // Supplier's Price
double? retailPrice; // Newarc Price
bool? status; // default to true
bool? isArchive; // default to false


//for UI
String? supplierName;
NewarcMaterialVariant? newarcMaterialVariant;



Map<String, Object?> toMap() {
  return {
    'newarcMaterialSupplierID': newarcMaterialSupplierID,
    'newarcMaterialVariantID': newarcMaterialVariantID,
    'naMaterialId': naMaterialId,
    'insertTimestamp': insertTimestamp,
    'modificationTimestamp': modificationTimestamp,
    'creationUid': creationUid,
    'modificationUid': modificationUid,
    'code': code,
    'basePrice': basePrice,
    'retailPrice': retailPrice,
    'status': status,
    'isArchive': isArchive,
    'newarcMaterialVariant': newarcMaterialVariant,
  };
}

NewarcMaterialVariantSupplierPrice.empty() {
  this.firebaseId = '';
  this.newarcMaterialSupplierID = '';
  this.newarcMaterialVariantID = '';
  this.naMaterialId = '';
  this.insertTimestamp = null;
  this.modificationTimestamp = null;
  this.creationUid = '';
  this.modificationUid = '';
  this.code = '';
  this.basePrice = 0.0;
  this.retailPrice = 0.0;
  this.status = true;
  this.isArchive = false;
  this.newarcMaterialVariant = null;
}

NewarcMaterialVariantSupplierPrice.fromDocument(Map<String, dynamic> data, String id) {
  this.firebaseId = id;

  try {
    this.newarcMaterialSupplierID = data['newarcMaterialSupplierID'];
    this.newarcMaterialVariantID = data['newarcMaterialVariantID'];
    this.naMaterialId = data['naMaterialId'];
    this.insertTimestamp = data['insertTimestamp'];
    this.modificationTimestamp = data['modificationTimestamp'];
    this.creationUid = data['creationUid'];
    this.modificationUid = data['modificationUid'];
    this.code = data['code'];
    this.basePrice = data['basePrice'];
    this.retailPrice = data['retailPrice'];
    this.status = data['status'];
    this.isArchive = data['isArchive'];

    if (data['newarcMaterialVariant'] != null) {
      if (data['newarcMaterialVariant'] is Map<String, dynamic>) {
        this.newarcMaterialVariant = NewarcMaterialVariant.fromDocument(
            data['newarcMaterialVariant'], '');
      } else {
        this.newarcMaterialVariant = null;
      }
    } else {
      this.newarcMaterialVariant = null;
    }

  } catch (e, s) {
    print({ 'NewarcMaterialVariantSupplierPrice Class Error ------->', e, s});
  }
}


}