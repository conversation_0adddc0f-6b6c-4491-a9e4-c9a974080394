class NewarcMaterialDimensionMeta {
  String? firebaseId;
  int? insertTimestamp;
  String? uid; //the Firebase Id of the logged in user
  String? materialID; // this is the Firebase ID of MaterialDimension
  String? name; //default to thickness
  String? value;
  String? unit;
  bool? status; /* true if active/false if inactive*/

  Map<String, Object?> toMap() {
    return {
      'insertTimestamp': insertTimestamp,
      'uid': uid,
      'materialID': materialID,
      'name': name,
      'value': value,
      'unit': unit,
      'status': status,
    };
  }

  NewarcMaterialDimensionMeta.empty() {
    this.firebaseId = '';
    this.insertTimestamp = null;
    this.uid = '';
    this.materialID = '';
    this.name = '';
    this.value = '';
    this.unit = '';
    this.status = true;
  }

  NewarcMaterialDimensionMeta.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;

    try {
      this.insertTimestamp = data['insertTimestamp'];
      this.uid = data['uid'];
      this.materialID = data['materialID'];
      this.name = data['name'];
      this.value = data['value'];
      this.unit = data['unit'];
      this.status = data['status'];

    } catch (e, s) {
      print({ 'NewarcMaterialDimensionMeta Class Error ------->', e, s});
    }
  }
}