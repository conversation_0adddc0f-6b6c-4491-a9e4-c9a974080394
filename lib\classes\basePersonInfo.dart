class BasePersonInfo{
  String? phone;
  String? email;
  String? name;
  String? surname;

  BasePersonInfo(Map<String, dynamic> userMap) {
    this.name = userMap['name'];
    this.surname = userMap['surname'];
    this.phone = userMap['phone'];
    this.email = userMap['email'] ;
  }

  BasePersonInfo.empty() {
    this.name = null;
    this.surname = null;
    this.phone = null;
    this.email = null;
  }

  BasePersonInfo.fromMap(Map<String, dynamic> data) {
    this.name = data['name'];
    this.surname = data['surname'];
    this.phone = data['phone'];
    this.email = data['email'];
  }

  Map<String, dynamic> toMap() {
    return {
      'name': this.name,
      'surname': this.surname,
      'phone': this.phone,
      'email': this.email,
    };
  }
}