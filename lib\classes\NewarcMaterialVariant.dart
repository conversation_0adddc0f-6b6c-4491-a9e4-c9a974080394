import 'package:newarc_materiotech/classes/NewarcMaterialManufacturerCollection.dart';

import 'NewarcMaterialSupplier.dart';
import 'NAMaterial.dart';
import 'NewarcMaterialDimension.dart';
import 'NewarcMaterialDimensionMeta.dart';

class NewarcMaterialVariant {
  String? firebaseId;
  int? insertTimestamp;
  int? modificationTimestamp;
  String? newarcMaterialDimensionID;
  String? newarcMaterialDimensionMetaID;
  String? naMaterialId;
  String? code;
  List<NewarcMaterialSupplier>? newarcMaterialSuppliers;
  bool? isArchive; // default to false
  String? creationUid; //the Firebase Id of the logged in user
  String? modificationUid;
  int? codeCounter;


  String? newarcMaterialVariantColorId; //--for MATERIALS - Porte Interne

// UI-related fields
  String? format; // For 'Formato'
  String? spessore; // For 'Spessore'
  String? color; //used in  MATERIALS - Porte Interne



  Map<String, Object?> toMap() {
    return {
      'insertTimestamp': insertTimestamp,
      'creationUid': creationUid,
      'modificationTimestamp': modificationTimestamp,
      'naMaterialId': naMaterialId,
      'newarcMaterialDimensionID': newarcMaterialDimensionID,
      'newarcMaterialDimensionMetaID': newarcMaterialDimensionMetaID,
      'code': code,
      'newarcMaterialSuppliers': newarcMaterialSuppliers,
      'isArchive': isArchive,
      'modificationUid': modificationUid,
      'codeCounter': codeCounter,
      'newarcMaterialVariantColorId': newarcMaterialVariantColorId,
    };
  }

  NewarcMaterialVariant.empty() {
    this.firebaseId = '';
    this.insertTimestamp = null;
    this.modificationTimestamp = null;
    this.creationUid = '';
    this.naMaterialId = '';
    this.newarcMaterialDimensionID = '';
    this.newarcMaterialDimensionMetaID = '';
    this.code = '';
    this.newarcMaterialSuppliers = [];
    this.isArchive = false;
    this.modificationUid = '';
    this.codeCounter = 0;
    this.newarcMaterialVariantColorId = '';
  }

  NewarcMaterialVariant.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;
    try {
      this.insertTimestamp = data['insertTimestamp'];
      this.modificationTimestamp = data['modificationTimestamp'];
      this.creationUid = data['creationUid'];
      this.naMaterialId = data['naMaterialId'];
      this.newarcMaterialDimensionID = data['newarcMaterialDimensionID'];
      this.newarcMaterialDimensionMetaID = data['newarcMaterialDimensionMetaID'];
      this.code = data['code'];
      this.isArchive = data['isArchive'];
      this.modificationUid = data['modificationUid'];
      this.codeCounter = data['codeCounter'];
      this.newarcMaterialVariantColorId = data['newarcMaterialVariantColorId'];
      if (data['newarcMaterialSuppliers'] != null) {
        for (var i = 0; i < data['newarcMaterialSuppliers'].length; i++) {
          this.newarcMaterialSuppliers?.add(
              NewarcMaterialSupplier.fromDocument(data['newarcMaterial'][i],''));
        }
      } else {
        this.newarcMaterialSuppliers = [];
      }
    } catch (e, s) {
      print({ 'NewarcMaterialVariantSupplierPrice Class Error ------->', e, s});
    }
  }

}

class NewarcMaterialVariantExtension extends NewarcMaterialVariant {
  NAMaterial? naMaterial;
  NewarcMaterialDimension? naMaterialDimension;
  NewarcMaterialDimensionMeta? naMaterialDimensionMeta;
  NewarcMaterialManufacturerCollection? naMaterialCollection;
  List<NewarcMaterialVariantExtension>? allVariants;

  // Constructor that calls the empty constructor of the parent class
  NewarcMaterialVariantExtension.empty() : super.empty() {
    naMaterial = null;
    naMaterialCollection = null;
    naMaterialDimension = null;
    naMaterialDimensionMeta = null;
    allVariants = null;
  }

  // Constructor that calls the fromDocument constructor of the parent class
  NewarcMaterialVariantExtension.fromDocument(Map<String, dynamic> data, String id) : super.fromDocument(data, id){
    naMaterial = null;
    naMaterialCollection = null;
    naMaterialDimension = null;
    naMaterialDimensionMeta = null;
    allVariants = null;
  }

  // Constructor with all related objects
  NewarcMaterialVariantExtension.withRelatedData(
      NewarcMaterialVariant variant,
      this.naMaterial,
      this.naMaterialCollection,
      this.naMaterialDimension,
      this.naMaterialDimensionMeta,
      this.allVariants) : super.fromDocument(variant.toMap(), variant.firebaseId!) {
    // // Copy all properties from the variant
    // firebaseId = variant.firebaseId;
    // insertTimestamp = variant.insertTimestamp;
    // modificationTimestamp = variant.modificationTimestamp;
    // newarcMaterialDimensionID = variant.newarcMaterialDimensionID;
    // newarcMaterialDimensionMetaID = variant.newarcMaterialDimensionMetaID;
    // naMaterialId = variant.naMaterialId;
    // code = variant.code;
    // newarcMaterialSuppliers = variant.newarcMaterialSuppliers;
    // isArchive = variant.isArchive;
    // creationUid = variant.creationUid;
    // modificationUid = variant.modificationUid;
    // codeCounter = variant.codeCounter;
    // newarcMaterialVariantColorId = variant.newarcMaterialVariantColorId;
    // format = variant.format;
    // spessore = variant.spessore;
    // color = variant.color;
    naMaterial = this.naMaterial;
    naMaterialCollection = this.naMaterialCollection;
    naMaterialDimension = this.naMaterialDimension;
    naMaterialDimensionMeta = this.naMaterialDimensionMeta;
    allVariants = this.allVariants;
  }

  String print() {
    if (naMaterial?.materialType == "tinte") {
      return naMaterial?.name ?? "NoName";
    } else {
      return '${naMaterial?.name ?? "NoName"} - ${naMaterialDimension?.width?.toInt() ?? "NoWidth"}x${naMaterialDimension?.length?.toInt() ?? "NoLength"}${naMaterialDimension?.dimensionUnit ?? "NoUnit"} ${naMaterialDimensionMeta?.value ?? "NoValue"}${naMaterialDimensionMeta?.unit ?? "NoUnit"}';
    }
  }
}