import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:newarc_materiotech/classes/baseAddressInfo.dart';

class RenovationContactAddress {

  int? created;
  String? id;
  BaseAddressInfo? addressInfo;
  bool? isArchived;
  

  RenovationContactAddress(Map<String, dynamic> newarcProjectMap) {
    this.created = newarcProjectMap['created'];
    this.addressInfo = newarcProjectMap.containsKey('addressInfo') && newarcProjectMap['addressInfo'] != null
    ? newarcProjectMap['addressInfo'] : BaseAddressInfo.empty();
    this.isArchived = newarcProjectMap['isArchived'] ?? false;
  }

  RenovationContactAddress.empty() {
    this.id = '';
    this.created = Timestamp.now().millisecondsSinceEpoch;
    this.addressInfo = BaseAddressInfo.empty();
    this.isArchived = false;

  }

  RenovationContactAddress.fromDocument(Map<String, dynamic> data, String id) {
    try {
      this.id = id;
      this.created = data['created'];
      this.addressInfo = data.containsKey('addressInfo') && data['addressInfo'] != null
      ? BaseAddressInfo.fromMap(data['addressInfo']) : BaseAddressInfo.empty();
      this.isArchived = data['isArchived'] ?? false;  
    } catch (e) {
      debugPrint(e.toString());
      debugPrintStack();
    }
    
  }

  Map<String, dynamic> toMap() {
    return {
      'created': this.created,
      'addressInfo': this.addressInfo!.toMap(),
      'isArchived': this.isArchived
    };
  }
}
