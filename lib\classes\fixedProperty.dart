class FixedProperty {
  String? propertyName;
  String? city;
  String? zone;
  String? propertyType;
  double? areaMQ;
  int? locals;
  int? floors;
  int? baths;
  String? refFirstName;
  String? refLastName;
  String? refPhone;
  String? refEmail;

  double? purchaseCost;
  bool? purchaseCostPaid;
  //let's rename these to something more understandable, like isPurchaseCostActive
  bool? isPurchaseCost;
  int? purchaseCostPaidDate;

  double? balance;
  bool? balancePaid;
  int? balancePaidDate;
  
  double? deposit;
  bool? depositPaid;
  int? depositPaidDate;

  double? notaryCost;
  bool? notaryCostPaid;
  int? notaryCostPaidDate;

  double? registrationTax;
  bool? registrationTaxPaid;
  int? registrationTaxPaidDate;

  double? ape;
  bool? apePaid;
  int? apePaidDate;

  double? condominiumFees;
  bool? condominiumFeesPaid;
  int? condominiumFeesPaidDate;

  double? loadUnloadInstallation;
  bool? loadUnloadInstallationPaid;
  int? loadUnloadInstallationPaidDate;

  // double? registrationTax2;
  double? electricalCond;
  bool? electricalCondPaid;
  int? electricalCondPaidDate;

  double? light;
  bool? lightPaid;
  int? lightPaidDate;

  double? moveGasCont;
  bool? moveGasContPaid;
  int? moveGasContPaidDate;

  double? heating;
  bool? heatingPaid;
  int? heatingPaidDate;

  double? posthumousInsurance;
  bool? posthumousInsurancePaid;
  int? posthumousInsurancePaidDate;

  double? carInsurance;
  bool? carInsurancePaid;
  int? carInsurancePaidDate;

  double? weldingCost;
  bool? weldingCostPaid;
  int? weldingCostPaidDate;

  bool? isDeposit;
  bool? isNotaryCost;
  bool? isRegistrationTax;
  bool? isApe;
  bool? isCondominiumFees;
  bool? isLoadUnloadInstallation;
  // bool? isRegistrationTax2;
  bool? isElectricalCond;
  bool? isLight;
  bool? isMoveGasCont;
  bool? isHeating;

  List<Map> refurbishmentCost = [];
  double? refurbishmentValue;
  String? refurbishmentInstallments;

  FixedProperty(Map<String, dynamic> fixedProperty) {
      this.propertyName = fixedProperty['propertyName'];
      this.zone = fixedProperty['zone'] == '' ? null : fixedProperty['zone'];
      this.city = fixedProperty['city'];
      this.propertyType = fixedProperty['propertyType'] == ''
          ? null
          : fixedProperty['propertyType'];
      this.areaMQ =
          fixedProperty['areaMQ'] == '' ? null : fixedProperty['areaMQ'];
      this.locals =
          fixedProperty['locals'] == '' ? null : fixedProperty['locals'];
      this.floors =
          fixedProperty['floors'] == '' ? null : fixedProperty['floors'];
      this.baths = fixedProperty['baths'] == '' ? null : fixedProperty['baths'];
      this.refFirstName = fixedProperty['refFirstName'];
      this.refLastName = fixedProperty['refLastName'];
      this.refPhone = fixedProperty['refPhone'];
      this.refEmail = fixedProperty['refEmail'];

      this.purchaseCost = fixedProperty['purchaseCost'] == ''
          ? null
          : fixedProperty['purchaseCost'];
      this.isPurchaseCost = fixedProperty['isPurchaseCost'] ?? false;

      this.balance =
          fixedProperty['balance'] == '' ? null : fixedProperty['balance'];
      this.deposit =
          fixedProperty['deposit'] == '' ? null : fixedProperty['deposit'];

      this.notaryCost = fixedProperty['notaryCost'] == ''
          ? null
          : fixedProperty['notaryCost'];
      this.registrationTax = fixedProperty['registrationTax'] == ''
          ? null
          : fixedProperty['registrationTax'];
      this.ape = fixedProperty['ape'] == '' ? null : fixedProperty['ape'];
      this.condominiumFees = fixedProperty['condominiumFees'] == ''
          ? null
          : fixedProperty['condominiumFees'];
      this.loadUnloadInstallation =
          fixedProperty['loadUnloadInstallation'] == ''
              ? null
              : fixedProperty['loadUnloadInstallation'];
      // this.registrationTax2 = fixedProperty['registrationTax2'] == ''
      //     ? null
      //     : fixedProperty['registrationTax2'];
      this.electricalCond = fixedProperty['electricalCond'] == ''
          ? null
          : fixedProperty['electricalCond'];
      this.light = fixedProperty['light'] == '' ? null : fixedProperty['light'];
      this.moveGasCont = fixedProperty['moveGasCont'] == ''
          ? null
          : fixedProperty['moveGasCont'];
      this.heating = fixedProperty['heating'] == '' ? null : fixedProperty['heating'];
      
      this.posthumousInsurance = fixedProperty['posthumousInsurance'] == '' ? null : fixedProperty['posthumousInsurance'];
      this.carInsurance = fixedProperty['carInsurance'] == '' ? null : fixedProperty['carInsurance'];
      this.weldingCost = fixedProperty['weldingCost'] == '' ? null : fixedProperty['weldingCost'];

      this.isDeposit = fixedProperty['deposit'] == '' ? false : true;
      this.isNotaryCost = fixedProperty['isNotaryCost'] ?? false;

      this.isRegistrationTax =
          fixedProperty['registrationTax'] == '' ? false : true;
      this.isApe = fixedProperty['ape'] == '' ? false : true;
      this.isCondominiumFees =
          fixedProperty['condominiumFees'] == '' ? false : true;
      this.isLoadUnloadInstallation =
          fixedProperty['loadUnloadInstallation'] == null ? false : true;
      // this.isRegistrationTax2 =
      //     fixedProperty['registrationTax2'] == '' ? false : true;
      this.isElectricalCond =
          fixedProperty['electricalCond'] == '' ? false : true;
      this.isLight = fixedProperty['light'] == '' ? false : true;
      this.isMoveGasCont = fixedProperty['moveGasCont'] == '' ? false : true;
      this.isHeating = fixedProperty['heating'] == '' ? false : true;

      this.purchaseCostPaid = fixedProperty['purchaseCostPaid'] ?? false;
      this.balancePaid = fixedProperty['balancePaid'] ?? false;
      this.depositPaid = fixedProperty['depositPaid'] ?? false;
      this.notaryCostPaid = fixedProperty['notaryCostPaid'] ?? false;
      this.registrationTaxPaid = fixedProperty['registrationTaxPaid'] ?? false;
      this.apePaid = fixedProperty['apePaid'] ?? false;
      this.condominiumFeesPaid = fixedProperty['condominiumFeesPaid'] ?? false;
      this.loadUnloadInstallationPaid =
          fixedProperty['loadUnloadInstallationPaid'] ?? false;
      this.electricalCondPaid = fixedProperty['electricalCondPaid'] ?? false;
      this.lightPaid = fixedProperty['lightPaid'] ?? false;
      this.moveGasContPaid = fixedProperty['moveGasContPaid'] ?? false;
      this.heatingPaid = fixedProperty['heatingPaid'] ?? false;
      this.posthumousInsurancePaid = fixedProperty['posthumousInsurancePaid'] ?? false;
      this.carInsurancePaid = fixedProperty['carInsurancePaid'] ?? false;
      this.weldingCostPaid = fixedProperty['weldingCostPaid'] ?? false;

      this.purchaseCostPaidDate = fixedProperty['purchaseCostPaidDate'];
      this.balancePaidDate = fixedProperty['balancePaidDate'];
      this.depositPaidDate = fixedProperty['depositPaidDate'];
      this.notaryCostPaidDate = fixedProperty['notaryCostPaidDate'];
      this.registrationTaxPaidDate = fixedProperty['registrationTaxPaidDate'];
      this.apePaidDate = fixedProperty['apePaidDate'];
      this.condominiumFeesPaidDate = fixedProperty['condominiumFeesPaidDate'];
      this.loadUnloadInstallationPaidDate =
          fixedProperty['loadUnloadInstallationPaidDate'];
      this.electricalCondPaidDate = fixedProperty['electricalCondPaidDate'];
      this.lightPaidDate = fixedProperty['lightPaidDate'];
      this.moveGasContPaidDate = fixedProperty['moveGasContPaidDate'];
      this.heatingPaidDate = fixedProperty['heatingPaidDate'];
      this.posthumousInsurancePaidDate = fixedProperty['posthumousInsurancePaidDate'];;
      this.carInsurancePaidDate = fixedProperty['carInsurancePaidDate'];
      this.weldingCostPaidDate = fixedProperty['weldingCostPaidDate'];

      if( fixedProperty['refurbishmentCost'] is List ) {
        this.refurbishmentCost = (fixedProperty['refurbishmentCost'] as List)
            .map((item) => item as Map<dynamic, dynamic>)
            .toList();
      } else {
        this.refurbishmentCost = fixedProperty['refurbishmentCost'];
      }
      
      this.refurbishmentValue = fixedProperty['refurbishmentValue'];
      this.refurbishmentInstallments =
          fixedProperty['refurbishmentInstallments'];  
    
    
  }

  FixedProperty.empty() {

    this.propertyName = '';
    this.zone = '';
    this.city = '';
    this.propertyType = '';
    this.areaMQ = 0;
    this.locals = 0;
    this.floors = 0;
    this.baths = 0;
    this.refFirstName = '';
    this.refLastName = '';
    this.refPhone = '';
    this.refEmail = '';

    this.purchaseCost = null;
    
    this.balance = null;
    
    this.deposit = null;
    this.notaryCost = null;
    this.registrationTax = null;
    this.ape = null;
    this.condominiumFees = null;
    this.loadUnloadInstallation = null;
    // this.registrationTax2 = null;
    this.electricalCond = null;
    this.light = null;
    this.moveGasCont = null;
    this.heating = null;
    this.posthumousInsurance = null;
    this.carInsurance = null;
    this.weldingCost = null;

    this.isPurchaseCost = false;
    
    this.isDeposit = false;
    this.isNotaryCost = false;
    this.isRegistrationTax = false;
    this.isApe = false;
    this.isCondominiumFees = false;
    this.isLoadUnloadInstallation = false;
    // this.isRegistrationTax2 = false;
    this.isElectricalCond = false;
    this.isLight = false;
    this.isMoveGasCont = false;
    this.isHeating = false;

    this.purchaseCostPaid = false;
    this.balancePaid = false;
    this.depositPaid = false;
    this.notaryCostPaid = false;
    this.registrationTaxPaid = false;
    this.apePaid = false;
    this.condominiumFeesPaid = false;
    this.loadUnloadInstallationPaid = false;
    this.electricalCondPaid = false;
    this.lightPaid = false;
    this.moveGasContPaid = false;
    this.heatingPaid = false;
    this.posthumousInsurancePaid = false;
    this.carInsurancePaid = false;
    this.weldingCostPaid = false;

    this.purchaseCostPaidDate = null;
    this.balancePaidDate = null;
    this.depositPaidDate = null;
    this.notaryCostPaidDate = null;
    this.registrationTaxPaidDate = null;
    this.apePaidDate = null;
    this.condominiumFeesPaidDate = null;
    this.loadUnloadInstallationPaidDate = null;
    this.electricalCondPaidDate = null;
    this.lightPaidDate = null;
    this.moveGasContPaidDate = null;
    this.heatingPaidDate = null;
    this.posthumousInsurancePaidDate = null;
    this.carInsurancePaidDate = null;
    this.weldingCostPaidDate = null;

    
    this.refurbishmentCost = [];
    this.refurbishmentInstallments = '';
    this.refurbishmentValue = 0;
    
  }

  Map<String, dynamic> toMap() {
    
      return {
        'propertyName': this.propertyName,
        'city': this.city,
        'zone': this.zone,
        'propertyType': this.propertyType,
        'areaMQ': this.areaMQ,
        'locals': this.locals,
        'floors': this.floors,
        'baths': this.baths,
        'refFirstName': this.refFirstName,
        'refLastName': this.refLastName,
        'refPhone': this.refPhone,
        'refEmail': this.refEmail,
        'purchaseCost': this.purchaseCost == null ? null : this.purchaseCost,

        'balance': this.balance == null ? null : this.balance,
        'deposit': this.deposit == null ? null : this.deposit,
        'notaryCost': this.notaryCost == null ? null : this.notaryCost,
        'registrationTax':
            this.registrationTax == null ? null : this.registrationTax,
        'ape': this.ape == null ? null : this.ape,
        'condominiumFees':
            this.condominiumFees == null ? null : this.condominiumFees,
        'loadUnloadInstallation': this.loadUnloadInstallation == null
            ? null
            : this.loadUnloadInstallation,
        // 'registrationTax2':
        //     this.registrationTax2 == null ? null : this.registrationTax2,
        'electricalCond':
            this.electricalCond == null ? null : this.electricalCond,
        'light': this.light == null ? null : this.light,
        'moveGasCont': this.moveGasCont == null ? null : this.moveGasCont,
        'heating': this.heating == null ? null : this.heating,
        'posthumousInsurance': this.posthumousInsurance == null ? null : this.posthumousInsurance,
        'carInsurance': this.carInsurance == null ? null : this.carInsurance,
        'weldingCost': this.weldingCost == null ? null : this.weldingCost,
        'purchaseCostPaid':
            this.purchaseCostPaid == null ? false : this.purchaseCostPaid,
        'balancePaid': this.balancePaid == null ? false : this.balancePaid,
        'depositPaid': this.depositPaid == null ? false : this.depositPaid,
        'notaryCostPaid':
            this.notaryCostPaid == null ? false : this.notaryCostPaid,
        'registrationTaxPaid':
            this.registrationTaxPaid == null ? false : this.registrationTaxPaid,
        'apePaid': this.apePaid == null ? false : this.apePaid,
        'condominiumFeesPaid':
            this.condominiumFeesPaid == null ? false : this.condominiumFeesPaid,
        'loadUnloadInstallationPaid': this.loadUnloadInstallationPaid == null
            ? false
            : this.loadUnloadInstallationPaid,
        'electricalCondPaid':
            this.electricalCondPaid == null ? false : this.electricalCondPaid,
        'lightPaid': this.lightPaid == null ? false : this.lightPaid,
        'moveGasContPaid':
            this.moveGasContPaid == null ? false : this.moveGasContPaid,
        'heatingPaid': this.heatingPaid == null ? false : this.heatingPaid,
        
        'posthumousInsurancePaid': this.posthumousInsurancePaid == null ? false : this.posthumousInsurancePaid,
        'carInsurancePaid': this.carInsurancePaid == null ? false : this.carInsurancePaid,
        'weldingCostPaid': this.weldingCostPaid == null ? false : this.weldingCostPaid,

        'purchaseCostPaidDate': this.purchaseCostPaidDate == null
            ? null
            : this.purchaseCostPaidDate,
        'balancePaidDate':
            this.balancePaidDate == null ? null : this.balancePaidDate,
        'depositPaidDate':
            this.depositPaidDate == null ? null : this.depositPaidDate,
        'notaryCostPaidDate':
            this.notaryCostPaidDate == null ? null : this.notaryCostPaidDate,
        'registrationTaxPaidDate': this.registrationTaxPaidDate == null
            ? null
            : this.registrationTaxPaidDate,
        'apePaidDate': this.apePaidDate == null ? null : this.apePaidDate,
        'condominiumFeesPaidDate': this.condominiumFeesPaidDate == null
            ? null
            : this.condominiumFeesPaidDate,
        'loadUnloadInstallationPaidDate':
            this.loadUnloadInstallationPaidDate == null
                ? null
                : this.loadUnloadInstallationPaidDate,
        'electricalCondPaidDate': this.electricalCondPaidDate == null
            ? null
            : this.electricalCondPaidDate,
        'lightPaidDate': this.lightPaidDate == null ? null : this.lightPaidDate,
        'moveGasContPaidDate':
            this.moveGasContPaidDate == null ? null : this.moveGasContPaidDate,
        'heatingPaidDate':
            this.heatingPaidDate == null ? null : this.heatingPaidDate,

        'posthumousInsurancePaidDate': this.posthumousInsurancePaidDate == null ? null : this.posthumousInsurancePaidDate,    
        'carInsurancePaidDate': this.carInsurancePaidDate == null ? null : this.carInsurancePaidDate,
        'weldingCostPaidDate': this.weldingCostPaidDate == null ? null : this.weldingCostPaidDate,    

        'refurbishmentCost':
            this.refurbishmentCost.length == 0 ? [] : this.refurbishmentCost,
        'refurbishmentValue':
            this.refurbishmentValue == null ? 0 : this.refurbishmentValue,
        'refurbishmentInstallments': this.refurbishmentInstallments == null
            ? ''
            : this.refurbishmentInstallments,
      };
    
    
  }

  FixedProperty.fromDocument(Map<String, dynamic> data) {

    this.propertyName = data['propertyName'];
    this.city = data['city'];
    this.zone = data['zone'];
    this.propertyType = data['propertyType'];
    this.areaMQ = data['areaMQ'];
    this.locals = data['locals'];
    this.floors = data['floors'];
    this.baths = data['baths'];
    this.refFirstName = data['refFirstName'];
    this.refLastName = data['refLastName'];
    this.refPhone = data['refPhone'];
    this.refEmail = data['refEmail'];
    this.purchaseCost = data['purchaseCost'];
    this.balance = data['balance'];
    this.deposit = data['deposit'];
    this.notaryCost = data['notaryCost'];
    this.registrationTax = data['registrationTax'];
    this.ape = data['ape'];
    this.condominiumFees = data['condominiumFees'];
    this.loadUnloadInstallation =
        data['loadUnloadInstallation'];
    // this.registrationTax2 = data['registrationTax2'];
    this.electricalCond = data['electricalCond'];
    this.light = data['light'];
    this.moveGasCont = data['moveGasCont'];
    this.heating = data['heating'];
    this.posthumousInsurance = data['posthumousInsurance'];
    this.carInsurance = data['carInsurance'];
    this.weldingCost = data['weldingCost'];

    this.isPurchaseCost = data['purchaseCost'] == null ? false : true;
    
    this.isDeposit =
        data['deposit'] == null ? false : true;
    this.isNotaryCost =
        data['notaryCost'] == null ? false : true;
    this.isRegistrationTax =
        data['registrationTax'] == null ? false : true;
    this.isApe = data['ape'] == null ? false : true;
    this.isCondominiumFees =
        data['condominiumFees'] == null ? false : true;
    this.isLoadUnloadInstallation =
        data['loadUnloadInstallation'] == null ? false : true;
    // this.isRegistrationTax2 =
    //     data['registrationTax2'] == null ? false : true;
    this.isElectricalCond =
        data['electricalCond'] == null ? false : true;
    this.isLight = data['light'] == null ? false : true;
    this.isMoveGasCont =
        data['moveGasCont'] == null ? false : true;
    this.isHeating = data['heating'] == null ? false : true;

    this.purchaseCostPaid = data['purchaseCostPaid'];
    this.balancePaid = data['balancePaid'];
    this.depositPaid = data['depositPaid'];
    this.notaryCostPaid = data['notaryCostPaid'];
    this.registrationTaxPaid = data['registrationTaxPaid'];
    this.apePaid = data['apePaid'];
    this.condominiumFeesPaid = data['condominiumFeesPaid'];
    this.loadUnloadInstallationPaid = data['loadUnloadInstallationPaid'];
    this.electricalCondPaid = data['electricalCondPaid'];
    this.lightPaid = data['lightPaid'];
    this.moveGasContPaid = data['moveGasContPaid'];
    this.heatingPaid = data['heatingPaid'];
    
    this.posthumousInsurancePaid = data['posthumousInsurancePaid'];
    this.carInsurancePaid = data['carInsurancePaid'];
    this.weldingCostPaid = data['weldingCostPaid'];

    this.purchaseCostPaidDate = data['purchaseCostPaidDate'];
    this.balancePaidDate = data['balancePaidDate'];
    this.depositPaidDate = data['depositPaidDate'];
    this.notaryCostPaidDate = data['notaryCostPaidDate'];
    this.registrationTaxPaidDate = data['registrationTaxPaidDate'];
    this.apePaidDate = data['apePaidDate'];
    this.condominiumFeesPaidDate = data['condominiumFeesPaidDate'];
    this.loadUnloadInstallationPaidDate = data['loadUnloadInstallationPaidDate'];
    this.electricalCondPaidDate = data['electricalCondPaidDate'];
    this.lightPaidDate = data['lightPaidDate'];
    this.moveGasContPaidDate = data['moveGasContPaidDate'];
    this.heatingPaidDate = data['heatingPaidDate'];
    
    this.posthumousInsurancePaidDate = data['posthumousInsurancePaidDate'];
    this.carInsurancePaidDate = data['carInsurancePaidDate'];
    this.weldingCostPaidDate = data['weldingCostPaidDate'];

    this.refurbishmentCost = data['refurbishmentCost'] == null  ? []  : List<Map<String, dynamic>>.from(data['refurbishmentCost']);
    this.refurbishmentInstallments = data['refurbishmentInstallments'] == null ? '' : data['refurbishmentInstallments'];
    this.refurbishmentValue = data['refurbishmentValue'] == null ? 0 : data['refurbishmentValue'];
    
    
    
  }
}


