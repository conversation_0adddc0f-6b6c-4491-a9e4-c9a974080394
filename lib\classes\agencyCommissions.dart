class AgencyCommissions {
  final double? adPrice;
  final String? agencyId;
  final bool? bonusInsPaid;
  final bool? bonusObiPaid;
  final bool? commissionInPaid;
  final bool? commissionOutPaid;

  final int? bonusInsPaidDate;
  final int? bonusObiPaidDate;
  final int? commissionInPaidDate;
  final int? commissionOutPaidDate;

  final double? bonusIns;
  final double? bonusObi;
  final double? commissionIn;
  final double? commissionOut;
  final int? mandateDuration;
  final double? minSale;
  final DateTime? saleDate;
  final double? saleTarget;

  AgencyCommissions({
    this.adPrice,
    this.agencyId,
    this.bonusInsPaid,
    this.bonusObiPaid,
    this.commissionInPaid,
    this.commissionOutPaid,
    this.bonusInsPaidDate,
    this.bonusObiPaidDate,
    this.commissionInPaidDate,
    this.commissionOutPaidDate,
    this.bonusIns,
    this.bonusObi,
    this.commissionIn,
    this.commissionOut,
    this.mandateDuration,
    this.minSale,
    this.saleDate,
    this.saleTarget,
  });

  factory AgencyCommissions.fromJson(Map<String, dynamic> json) =>
      AgencyCommissions(
        adPrice: json['adPrice'] as double?,
        agencyId: json['agencyId'] as String?,
        bonusInsPaid: json['bonusInsPaid'] as bool?,
        bonusObiPaid: json['bonusObiPaid'] as bool?,
        commissionInPaid: json['commissionInPaid'] as bool?,
        commissionOutPaid: json['commissionOutPaid'] as bool?,
        
        bonusInsPaidDate: json['bonusInsPaidDate'] as int?,
        bonusObiPaidDate: json['bonusObiPaidDate'] as int?,
        commissionInPaidDate: json['commissionInPaidDate'] as int?,
        commissionOutPaidDate: json['commissionOutPaidDate'] as int?,
        
        bonusIns: json['bonusIns'] as double?,
        bonusObi: json['bonusObi'] as double?,
        commissionIn: json['commissionIn'] as double?,
        commissionOut: json['commissionOut'] as double?,
        mandateDuration: json['mandateDuration'] as int?,
        minSale: json['minSale'] as double?,
        saleDate: json['saleDate'] != null
            ? DateTime.parse(json['saleDate'] as String)
            : null,
        saleTarget: json['saleTarget'] as double?,
      );

  Map<String, dynamic> toJson() => {
        'adPrice': adPrice,
        'agencyId': agencyId, 
        'bonusInsPaid': bonusInsPaid,
        'bonusobiPaid': bonusObiPaid,
        'commissionInPaid': commissionInPaid,
        'commissionOutPaid': commissionOutPaid,
        'bonusInsPaidDate': bonusInsPaidDate,
        'bonusobiPaidDate': bonusObiPaidDate,
        'commissionInPaidDate': commissionInPaidDate,
        'commissionOutPaidDate': commissionOutPaidDate,
        
        'bonusIns': bonusIns,
        'bonusObi': bonusObi,
        'commissionIn': commissionIn,
        'commissionOut': commissionOut,
        'mandateDuration': mandateDuration,
        'minSale': minSale,
        'saleDate': saleDate?.toIso8601String(),
        'saleTarget': saleTarget,
      };
}
