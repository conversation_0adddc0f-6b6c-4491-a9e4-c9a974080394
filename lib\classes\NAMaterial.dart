

import 'NewarcMaterialVariant.dart';

class NAMaterial {
  String? firebaseId;
  String? newarcManufacturerID;
  String? newarcProductCollectionID;
  List? newarcSubCategoryID;

  // Identification
  String? name; // Product Name
  Map? coverImagePath;
  List<Map>? applicationImagesPath;
  List<NewarcMaterialVariant>? newarcMaterialVariants;
  bool? configurationStatus;
  String? code;
  int? codeCounter;
  String? materialType;
  bool? isArchive; // default to false
  int? insertTimestamp;
  int? modificationTimestamp;
  String? creationUid;
  String? modificationUid;
  bool? isAnySupplierPriceAvailable;
  bool? isMateriotechAvailable;

  //--for MATERIALS - Porte Interne
  String? description;
  List? newarcTipologiaIDS;
  List? newarcDimensionIDS;

  // only for UI
  String? collectionName;
  String? manufacturerName;




  Map<String, Object?> toMap() {
    return {
      'newarcManufacturerID': newarcManufacturerID,
      'newarcProductCollectionID': newarcProductCollectionID,
      'newarcSubCategoryID': newarcSubCategoryID,
      'name': name,
      'applicationImagesPath': applicationImagesPath,
      'coverImagePath': coverImagePath,
      'configurationStatus': configurationStatus,
      'newarcMaterialVariants': newarcMaterialVariants,
      'code': code,
      'codeCounter': codeCounter,
      'materialType': materialType,
      'isArchive': isArchive,
      'insertTimestamp': insertTimestamp,
      'modificationTimestamp': modificationTimestamp,
      'creationUid': creationUid,
      'modificationUid': modificationUid,
      'isAnySupplierPriceAvailable': isAnySupplierPriceAvailable,
      'isMateriotechAvailable': isMateriotechAvailable,
      'description': description,
      'newarcTipologiaIDS': newarcTipologiaIDS,
      'newarcDimensionIDS': newarcDimensionIDS,
    };
  }

  NAMaterial.empty() {
    this.firebaseId = '';
    this.name = '';
    this.applicationImagesPath = [];
    this.coverImagePath = {};
    this.configurationStatus = false;
    this.newarcManufacturerID = '';
    this.newarcSubCategoryID = [];
    this.newarcMaterialVariants = [];
    this.newarcProductCollectionID = '';
    this.code = '';
    this.codeCounter = 0;
    this.materialType = '';
    this.isArchive = false;
    this.insertTimestamp = null;
    this.modificationTimestamp = null;
    this.creationUid = '';
    this.modificationUid = '';
    this.isAnySupplierPriceAvailable = false;
    this.isMateriotechAvailable = false;
    this.description = '';
    this.newarcTipologiaIDS = [];
    this.newarcDimensionIDS = [];
  }

  NAMaterial.fromDocument(Map<String, dynamic> data, String id) {
    this.firebaseId = id;

    try {
      this.newarcManufacturerID = data['newarcManufacturerID'];
      this.newarcProductCollectionID = data['newarcProductCollectionID'];
      this.newarcSubCategoryID = data['newarcSubCategoryID'];
      this.name = data['name'];
      this.coverImagePath = data['coverImagePath'];
      this.configurationStatus = data['configurationStatus'];
      if (data['newarcMaterialVariants'] != null) {
        for (var i = 0; i < data['newarcMaterialVariants'].length; i++) {
          this.newarcMaterialVariants?.add(NewarcMaterialVariant.fromDocument(data['newarcMaterialVariants'][i],''));
        }
      } else {
        this.newarcMaterialVariants = [];
      }
      if (data['applicationImagesPath'] != null && data['applicationImagesPath'] is List) {
        this.applicationImagesPath = List<Map<String, dynamic>>.from(data['applicationImagesPath']);
      } else {
        this.applicationImagesPath = [];
      }
      this.code = data['code'];
      this.codeCounter = data['codeCounter'];
      this.materialType = data['materialType'];
      this.isArchive = data['isArchive'];
      this.insertTimestamp = data['insertTimestamp'];
      this.modificationTimestamp = data['modificationTimestamp'];
      this.creationUid = data['creationUid'];
      this.modificationUid = data['modificationUid'];
      this.isAnySupplierPriceAvailable = data['isAnySupplierPriceAvailable'];
      this.isMateriotechAvailable = data['isMateriotechAvailable'];
      this.description = data['description'];
      this.newarcTipologiaIDS = data['newarcTipologiaIDS'] ?? [];
      this.newarcDimensionIDS = data['newarcDimensionIDS'] ?? [];

    } catch (e, s) {
      print({ 'NAMaterial Class Error ------->', e, s});
    }
  }
}