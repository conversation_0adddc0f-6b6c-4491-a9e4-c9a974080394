import 'package:flutter/material.dart';
import 'dart:math';
import 'package:newarc_materiotech/classes/NewarcMaterialVariant.dart';
import 'package:newarc_materiotech/utils/storage.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:newarc_materiotech/utils/various.dart';


class NewarcMaterialIcon extends StatefulWidget {
  final NewarcMaterialVariantExtension? material;
  final double? height;
  final double? width;
  final Function? onCloseTap;

  const NewarcMaterialIcon({
    super.key,
    this.material,
    this.height = 50,
    this.width = 50,
    this.onCloseTap,
  });

  @override
  State<NewarcMaterialIcon> createState() => _NewarcMaterialIconState();
}

class _NewarcMaterialIconState extends State<NewarcMaterialIcon> {
  int currentPageIndex = 0;

  // Cache for application image URLs
  final Map<int, String> _imageUrlCache = {};

  // Method to get variants with unique value attributes
  List<NewarcMaterialVariantExtension> getUniqueVariantsByValue() {
    if (widget.material?.allVariants == null) return [];

    // Use a set to track unique values
    final Set<String> uniqueValues = {};
    final List<NewarcMaterialVariantExtension> uniqueVariants = [];

    for (var variant in widget.material!.allVariants!) {
      final value = variant.naMaterialDimensionMeta?.value ?? "";

      // If this value hasn't been seen before, add it to our results
      if (!uniqueValues.contains(value)) {
        uniqueValues.add(value);
        uniqueVariants.add(variant);
      }
    }

    return uniqueVariants;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate sizes based on available space
        final availableWidth = constraints.maxWidth;
        final availableHeight = constraints.maxHeight;
        // Select min between width and height
        var availableSize = availableWidth < availableHeight ? availableWidth : availableHeight;
        final fontSize = availableSize * 0.06; // Responsive font size
        availableSize -= 6;
        availableSize -= fontSize * 3;

        return Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            FutureBuilder(
              future: printUrl(widget.material!.naMaterial!.coverImagePath!["location"], "", widget.material!.naMaterial!.coverImagePath!["filename"]),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Stack(
                    children: [
                      GestureDetector(
                        onTap: () {
                          if ((widget.material?.naMaterial?.materialType ?? "tinte") != "tinte") {
                            showMaterialPopup(context);
                          }
                        },
                        child: Container(
                          width: availableSize,
                          height: availableSize,
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              // border: Border.all(
                              //     color: Color(0xffDBDBDB),
                              //     width: 0),
                              color: Color(0xffF9F9F9),
                              image: DecorationImage(
                                  image: FadeInImage.assetNetwork(
                                    placeholder: 'assets/icons/file.png',
                                    image: snapshot.data!,
                                    fit: BoxFit.cover,
                                  ).image,
                                  fit: BoxFit.cover
                              )
                          ),
                        )
                      ),
                      Positioned(
                        top: 3,
                        right: 3,
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: (){
                              setState(() {
                                widget.onCloseTap!.call(widget.material!.firebaseId);
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Theme.of(context).primaryColorLight,
                                boxShadow: const [BoxShadow(blurRadius: 2)]
                              ),
                              child: Padding(
                                padding: EdgeInsets.all(4),
                                child: SvgPicture.asset(
                                  'assets/icons/close_popup.svg',
                                  height: availableWidth * 0.08, // Responsive icon size
                                  colorFilter: ColorFilter.mode(
                                    Theme.of(context).primaryColor,
                                    BlendMode.srcIn
                                  ),
                                )
                              )
                            )
                          ),
                        )
                      )
                    ]
                  );
                }
                else if (snapshot.hasError) {
                  return SizedBox(
                    width: double.infinity,
                    height: double.infinity,
                    child: Center(
                      child: Text(
                        snapshot.error.toString(),
                        style: TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      )
                    )
                  );
                }
                else {
                  return Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                    )
                  );
                }
              }
            ),
            Padding(
              padding: EdgeInsets.only(top: 4, bottom: 2),
              child: Text(
                widget.material!.print().replaceAll(" - ", "\n"),
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'Raleway',
                  color: Theme.of(context).primaryColor,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            )
          ],
        );
      }
    );
  }

  // Method to get image URL with caching to avoid repeated Firebase Storage calls
  Future<String> _getImageUrl(int index) async {
    // Check if URL is already in cache
    if (_imageUrlCache.containsKey(index)) {
      return _imageUrlCache[index]!;
    }

    // If not in cache, fetch it from Firebase Storage
    try {
      final location = widget.material!.naMaterial!.applicationImagesPath![index]["location"];
      final filename = widget.material!.naMaterial!.applicationImagesPath![index]["filename"];

      // Fetch the URL
      final url = await printUrl(location, "", filename);

      // Cache the URL
      _imageUrlCache[index] = url;

      return url;
    } catch (e) {
      print('Error fetching image URL: $e');
      return '';
    }
  }

  Future<String> _getFormatImageUrl(int index) async {
    try {
      final location = widget.material!.allVariants![index].naMaterialDimension!.picturePath!["location"];
      final filename = widget.material!.allVariants![index].naMaterialDimension!.picturePath!["filename"];

      // Fetch the URL
      final url = await printUrl(location, "", filename);

      return url;
    } catch (e) {
      print('Error fetching format image URL: $e');
      return '';
    }
  }

  void showMaterialPopup(BuildContext context) {
    showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          final verticalPopupPadding = MediaQuery.of(context).size.height*.05;
          final horizontalPopupPadding = MediaQuery.of(context).size.width*.05;
          final double headerCorpusRatio = 1.5/10;
          final double horizontalSpacer = 30;
          return Center(
            child: Stack(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    vertical: verticalPopupPadding,
                    horizontal: horizontalPopupPadding,
                  ),
                  color: Theme.of(context).primaryColorLight,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: (MediaQuery.of(context).size.width - 2*horizontalPopupPadding)/2,
                        height: (MediaQuery.of(context).size.height - 2*verticalPopupPadding)*headerCorpusRatio,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children:[
                            Text(
                              widget.material!.naMaterial!.materialType!.toCapitalized(),
                              style: TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w500,
                                fontFamily: 'Raleway',
                                color: Color(0xff808080),
                              ),
                            ),
                            Text(
                              widget.material!.naMaterial!.name!.toCapitalized(),
                              style: TextStyle(
                                fontSize: 30,
                                fontWeight: FontWeight.w800,
                                fontFamily: 'Raleway',
                                color: Theme.of(context).primaryColorDark,
                              ),
                            ),
                          ]
                        ),
                      ),
                      Row(
                        children:[
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 2*horizontalPopupPadding - horizontalSpacer)/2,
                            height: (MediaQuery.of(context).size.height - 3*verticalPopupPadding)*(1-headerCorpusRatio),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'La collezione',
                                      style: TextStyle(
                                        fontSize: 17,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Raleway',
                                        color: Color(0xff808080),
                                      ),
                                    ),
                                    SizedBox(height: 10),
                                    Text(
                                      widget.material!.naMaterialCollection?.description ?? "",
                                      style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Raleway',
                                        color: Theme.of(context).primaryColorDark,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 30),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Spessori disponibili',
                                      style: TextStyle(
                                        fontSize: 17,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Raleway',
                                        color: Color(0xff808080),
                                      ),
                                    ),
                                    SizedBox(height: 10),
                                    SizedBox(
                                      height: 30,
                                      child: ListView(
                                        scrollDirection: Axis.horizontal,
                                        children: [
                                          ...getUniqueVariantsByValue().map((variant) => Padding(
                                            padding: EdgeInsets.only(right: 10),
                                            child: Text(
                                              (variant.naMaterialDimensionMeta!.value ?? "") + (variant.naMaterialDimensionMeta!.unit ?? ""),
                                              style: TextStyle(
                                                fontSize: 15,
                                                fontWeight: FontWeight.w600,
                                                fontFamily: 'Raleway',
                                                color: Theme.of(context).primaryColorDark,
                                              ),
                                            ),
                                          ))
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                                SizedBox(height: 30),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Formati disponibili',
                                      style: TextStyle(
                                        fontSize: 17,
                                        fontWeight: FontWeight.w500,
                                        fontFamily: 'Raleway',
                                        color: Theme.of(context).primaryColor,
                                      ),
                                    ),
                                    SizedBox(height: 10),
                                    widget.material!.naMaterialDimension!.picturePath!.isNotEmpty
                                    ? SizedBox(
                                      height: 160, // Fixed height for the ListView
                                      child: ListView.builder(
                                        scrollDirection: Axis.horizontal,
                                        itemCount: widget.material!.allVariants!.length,
                                        itemBuilder: (context, index) {
                                          return FutureBuilder(
                                            future: _getFormatImageUrl(index),
                                            builder: (context, snapshot) {
                                              NewarcMaterialVariantExtension variant = widget.material!.allVariants![index];
                                              String variantFormat = "${variant.naMaterialDimension?.width?.toInt() ?? ""}x${variant.naMaterialDimension?.length?.toInt() ?? ""}${variant.naMaterialDimension?.dimensionUnit ?? ""}\n${variant.naMaterialDimensionMeta?.value ?? ""}${variant.naMaterialDimensionMeta?.unit ?? ""}";
                                              if (snapshot.hasData) {
                                                return Padding(
                                                  padding: const EdgeInsets.only(right: 8.0),
                                                  child: Column(
                                                    children: [
                                                      Container(
                                                        width: 100,
                                                        height: 120,
                                                        clipBehavior: Clip.antiAlias,
                                                        decoration: BoxDecoration(
                                                          borderRadius: BorderRadius.all(Radius.zero),
                                                          image: DecorationImage(
                                                            image: FadeInImage.assetNetwork(
                                                              placeholder:
                                                                  'assets/icons/file.png',
                                                              image: snapshot.data!,
                                                              fit: BoxFit.fitHeight,
                                                            ).image,
                                                            fit: BoxFit.fitHeight,
                                                          )
                                                        ),
                                                      ),
                                                      Text(
                                                        variantFormat,
                                                        textAlign: TextAlign.center,
                                                        style: TextStyle(
                                                          fontSize: 12,
                                                          fontWeight: FontWeight.w500,
                                                          fontFamily: 'Raleway',
                                                          color: Theme.of(context).primaryColorDark,
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                );
                                              } else if (snapshot.hasError) {
                                                return SizedBox(
                                                  width: 50,
                                                  height: 160, // Match the parent container height
                                                  child: Center(
                                                    child: Text(
                                                      snapshot.error.toString(),
                                                      style: TextStyle(color: Colors.red),
                                                      textAlign: TextAlign.center,
                                                    )
                                                  )
                                                );
                                              }
                                              else {
                                                return SizedBox(
                                                  width: 50,
                                                  height: 160, // Match the parent container height
                                                  child: Center(
                                                    child: CircularProgressIndicator(
                                                      color: Colors.white,
                                                    )
                                                  )
                                                );
                                              }
                                            }
                                          );
                                        }
                                      )
                                    )
                                    : Text(
                                      'Nessun formato disponibile',
                                      style: TextStyle(
                                        fontSize: 15,
                                        fontWeight: FontWeight.w800,
                                        fontFamily: 'Raleway',
                                        color: Theme.of(context).primaryColorDark,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: horizontalSpacer,),
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 2*horizontalPopupPadding - horizontalSpacer)/2,
                            height: (MediaQuery.of(context).size.height - 3*verticalPopupPadding)*(1-headerCorpusRatio),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Esempio applicazione',
                                  style: TextStyle(
                                    fontSize: 17,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Raleway',
                                    color: Color(0xff808080),
                                  ),
                                ),
                                SizedBox(height: 10),
                                widget.material!.naMaterial!.applicationImagesPath!.isNotEmpty
                                ? FutureBuilder(
                                  key: ValueKey(currentPageIndex),
                                  future: _getImageUrl(currentPageIndex),
                                  builder: (context, snapshot) {
                                    // handleTVComunication('send', widget.material!.code!, widget.material!.applicationImages![currentPageIndex]);
                                    if (snapshot.hasData) {
                                      return Expanded(
                                        child: Stack(
                                          children: [
                                            Container(
                                              clipBehavior: Clip.antiAlias,
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.all(Radius.zero),
                                                image: DecorationImage(
                                                  image: FadeInImage.assetNetwork(
                                                    placeholder:
                                                        'assets/icons/file.png', // Local loading image
                                                    image: snapshot.data!,
                                                    fit: BoxFit.cover,
                                                  ).image,
                                                  fit: BoxFit.cover,
                                                )
                                              ),
                                            ),
                                            Positioned(
                                              top: (MediaQuery.of(context).size.height - 2*verticalPopupPadding)*(1-headerCorpusRatio)*.8/2,
                                              right: 10,
                                              child: MouseRegion(
                                                cursor: SystemMouseCursors.click,
                                                child: GestureDetector(
                                                  onTap: (){
                                                    if (currentPageIndex == widget.material!.naMaterial!.applicationImagesPath!.length -1) {
                                                      setState(() {
                                                        currentPageIndex = 0;
                                                      });
                                                    }
                                                    else {
                                                      setState(() {
                                                      currentPageIndex += 1;
                                                      });
                                                    }
                                                  },
                                                  child: Transform.rotate(
                                                    angle: pi*3/2,
                                                    child: SvgPicture.asset(
                                                      'assets/icons/arrow_down.svg',
                                                      height: 15,
                                                      colorFilter: ColorFilter.mode(
                                                        Theme.of(context).primaryColorLight,
                                                        BlendMode.srcIn
                                                      )
                                                    )
                                                  )
                                                ),
                                              )
                                            ),
                                            Positioned(
                                              top: (MediaQuery.of(context).size.height - 2*verticalPopupPadding)*(1-headerCorpusRatio)*.8/2,
                                              left: 10,
                                              child: MouseRegion(
                                                cursor: SystemMouseCursors.click,
                                                child: GestureDetector(
                                                  onTap: (){
                                                    if (currentPageIndex == 0) {
                                                      setState(() {
                                                        currentPageIndex = widget.material!.naMaterial!.applicationImagesPath!.length -1;
                                                      });
                                                    }
                                                    else {
                                                      setState(() {
                                                        currentPageIndex -= 1;
                                                      });
                                                    }
                                                  },
                                                  child: Transform.rotate(
                                                    angle: pi/2,
                                                    child: SvgPicture.asset(
                                                      'assets/icons/arrow_down.svg',
                                                      height: 15,
                                                      colorFilter: ColorFilter.mode(
                                                        Theme.of(context).primaryColorLight,
                                                        BlendMode.srcIn
                                                      )
                                                    )
                                                  )
                                                ),
                                              )
                                            ),
                                          ],
                                        )
                                      );
                                    }
                                    else if (snapshot.hasError) {
                                      return Expanded(
                                        child: SizedBox(
                                          child: Text(
                                            snapshot.error.toString(),
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w800,
                                              fontFamily: 'Raleway',
                                              color: Theme.of(context).primaryColorLight,
                                            ),
                                          )
                                        )
                                      );
                                    }
                                    else {
                                      return Expanded(
                                        child: SizedBox(
                                          child: Center(child: CircularProgressIndicator(
                                            color: Colors.black,
                                          ))
                                        )
                                      );
                                    }
                                  }
                                )
                                : Expanded(
                                  child: SizedBox(
                                    child: Text(
                                      'Nessun esempio di applicazione trovato',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w800,
                                        fontFamily: 'Raleway',
                                        color: Theme.of(context).primaryColorDark,
                                      ),
                                    )
                                  )
                                ),
                              ]
                            )
                          ),
                        ]
                      )
                    ],
                  ),
                ),
                Positioned(
                  top: verticalPopupPadding,
                  right: horizontalPopupPadding,
                  child: MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: (){
                        // handleTVComunication('shut', null, null);
                        Navigator.pop(context);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).primaryColorDark,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: SvgPicture.asset(
                            'assets/icons/close_popup.svg',
                            height: 15,
                            colorFilter: ColorFilter.mode(
                              Theme.of(context).primaryColorLight,
                              BlendMode.srcIn
                            )
                          )
                        )
                      )
                    ),
                  )
                )
              ],
            )
          );
        });
      }
    );
  }
}