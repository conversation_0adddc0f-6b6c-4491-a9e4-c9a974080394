class Process{
  int? indexPlace;
  String? uid; // unique string to associate Process with ProjectJob
  String? vendorUserId;
  String? vendor;
  String? activity;
  double? cost;
  int? agreedInstallments;
  List<PaymentInstallment>? installments = [];
  bool? contractor;
  bool? hasPenalty;
  double? agreedPenalty;
  int? jobStartTimestamp;
  int? jobEndTimestamp;
  bool? isUnique;

  Process(Map<String, dynamic> fixedProperty) {
    this.indexPlace = fixedProperty['indexPlace'];
    this.uid = fixedProperty['uid'];
    this.vendorUserId = fixedProperty['vendorUserId'];
    this.vendor = fixedProperty['vendor'];
    this.activity = fixedProperty['activity'] == null ? '' : fixedProperty['activity'];
    this.cost = fixedProperty['cost'] == '' ? null : fixedProperty['cost'];
    this.agreedInstallments = fixedProperty['agreedInstallments'] == '' ? 0 : fixedProperty['agreedInstallments'];
    this.installments = fixedProperty['installments'] == '' ? [] : fixedProperty['installments'];
    this.contractor = fixedProperty['contractor'] == '' ? false : fixedProperty['contractor'];
    this.hasPenalty = fixedProperty['hasPenalty'] == '' ? false : fixedProperty['hasPenalty'];
    this.agreedPenalty = fixedProperty['agreedPenalty'] == '' ? '' : fixedProperty['agreedPenalty'];
    this.jobStartTimestamp = fixedProperty['jobStartTimestamp'] == '' ? '' : fixedProperty['jobStartTimestamp'];
    this.jobEndTimestamp = fixedProperty['jobEndTimestamp'] == '' ? '' : fixedProperty['jobEndTimestamp'];
    this.isUnique = fixedProperty['isUnique'] == '' ? false : fixedProperty['isUnique'];

    this.installments = fixedProperty['installments'] == null ? [] : fixedProperty['installments'];
  }

  Process.empty(){
    this.indexPlace = -1;
    this.vendorUserId = '';
    this.vendor = '';
    this.uid = '';
    this.activity = '';
    this.cost = 0;
    this.agreedInstallments = 1;
    this.installments = [];
    this.contractor = false;
    this.hasPenalty = false;
    this.agreedPenalty = 0;
    this.jobStartTimestamp = DateTime.now().millisecondsSinceEpoch;
    this.jobEndTimestamp = DateTime.now().millisecondsSinceEpoch;
    this.isUnique = false;
    
  }

  Map<String, dynamic> toMap() {
    return {
      'indexPlace': this.indexPlace,
      'uid': this.uid,
      'vendorUserId': this.vendorUserId,
      'vendor': this.vendor,
      'activity': this.activity,
      'cost': this.cost,
      'agreedInstallments': this.agreedInstallments,
      'installments': this.installments!.map((e) => e.toMap()).toList(),
      'contractor': this.contractor,
      'hasPenalty': this.hasPenalty,
      'agreedPenalty': this.agreedPenalty,
      'jobStartTimestamp': this.jobStartTimestamp,
      'jobEndTimestamp': this.jobEndTimestamp,
      'isUnique': this.isUnique
      
    };
  }

  Process.fromDocument(Map<String, dynamic> data, int index) {

    try {
      // print({ 'data-->', data});
      this.indexPlace = index;
      this.vendorUserId = data['vendorUserId'];
      this.vendor = data['vendor'];
      this.uid = data['uid'];
      this.activity = data['activity'] == '' ? '' : data['activity'];
      this.cost = data['cost'] == '' ? null : data['cost'];
      this.agreedInstallments = data['agreedInstallments'] == '' ? 0 : data['agreedInstallments'];
      this.contractor = data['contractor'] == '' ? false : data['contractor'];
      this.hasPenalty = data['hasPenalty'] == null ? false : data['hasPenalty'];
      this.agreedPenalty = data['agreedPenalty'] == '' ? '' : data['agreedPenalty'];

      this.jobStartTimestamp = data['jobStartTimestamp'] == '' ? '' : data['jobStartTimestamp'];
      this.jobEndTimestamp = data['jobEndTimestamp'] == '' ? '' : data['jobEndTimestamp'];
      this.isUnique = data['isUnique'] == null ? false : data['isUnique']; 
      if( data['installments'] != null ) {
        for (var i = 0; i < data['installments'].length ; i++) {
          this.installments!.add( PaymentInstallment.fromDocument(data['installments'][i], (i+1), data['installments'].length ) );
        }
      } else {
        this.installments = [];
      }

      // print({'data-->!',this.toMap()}); 
    } catch (e, s) {
      // print({'process.dart', e, s});
    }
    
  }

}

class PaymentInstallment {

  int? counter;
  double? amount;
  bool? status; //true:paid, false: not paid
  int? paidOn; //timestamp of the payment date
  bool? isEndOfList;

  PaymentInstallment(Map<String, dynamic> fixedProperty) {
    // try {
      this.amount = fixedProperty['amount'] == '' ? null : fixedProperty['amount'];
      this.status = fixedProperty['status'] == null ? false : fixedProperty['status'];
      this.counter = fixedProperty['counter'] == null ? null : fixedProperty['counter'];
      this.isEndOfList =  fixedProperty['isEndOfList'] == null || fixedProperty['isEndOfList'] == '' ? false : fixedProperty['isEndOfList'];
      this.paidOn = fixedProperty['paidOn'] == null ? 0 : fixedProperty['paidOn'];  
    // } catch (e,s) {
    //   print({'installments',e,s});
    // }

    
  }

  PaymentInstallment.empty(){
    this.counter = 0;
    this.amount = 0;
    this.status = false;
    this.paidOn = 0;
    this.isEndOfList = false;
    
  }

  Map<String, dynamic> toMap() {
    return {
      'amount': this.amount,
      'status': this.status,
      'paidOn': this.paidOn
    };
  }

  PaymentInstallment.fromDocument(Map<String, dynamic> data, int counter, int count) {
    this.counter = counter;
    this.amount = data['amount'];
    this.status = data['status'];
    this.paidOn = data['paidOn'] != null ? data['paidOn'] : 0;
    this.isEndOfList = counter == count ? true : false;
  }

}